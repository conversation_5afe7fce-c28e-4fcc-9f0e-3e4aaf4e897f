{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\tours\\\\BannerStepPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Button, Typography, IconButton, DialogActions, MobileStepper, LinearProgress } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BannerStepPreview = ({\n  showBannerenduser,\n  setShowBannerenduser,\n  initialGuideData,\n  backgroundC,\n  totalSteps,\n  savedGuideData,\n  progress,\n  onClose\n}) => {\n  _s();\n  var _initialGuideData$Gui, _bannerSteps$Canvas2, _bannerSteps$TextFiel, _bannerSteps$ButtonSe, _bannerSteps$ButtonSe2, _Modal$DismissOption, _savedGuideData$Guide, _savedGuideData$Guide2, _savedGuideData$Guide3, _initialGuideData$Gui2, _initialGuideData$Gui3, _initialGuideData$Gui4, _initialGuideData$Gui5, _initialGuideData$Gui6, _initialGuideData$Gui7, _initialGuideData$Gui8, _initialGuideData$Gui9;\n  const {\n    btnBgColor,\n    btnTextColor,\n    btnBorderColor,\n    setCurrentStep,\n    currentStep,\n    selectedOption,\n    ProgressColor,\n    setBposition\n  } = useDrawerStore(state => state);\n  const [showBanner, setShowBanner] = useState(true);\n  const {\n    setImageSrc,\n    imageSrc,\n    htmlContent,\n    sectionColor\n  } = useDrawerStore(state => state);\n  const bannerSteps = (_initialGuideData$Gui = initialGuideData.GuideStep) === null || _initialGuideData$Gui === void 0 ? void 0 : _initialGuideData$Gui[currentStep - 1];\n  // Update the position when the component mounts or when the canvas position changes\n  useEffect(() => {\n    var _bannerSteps$Canvas;\n    // Get the current Canvas position from the step\n    const currentPosition = bannerSteps === null || bannerSteps === void 0 ? void 0 : (_bannerSteps$Canvas = bannerSteps.Canvas) === null || _bannerSteps$Canvas === void 0 ? void 0 : _bannerSteps$Canvas.Position;\n    if (currentPosition) {\n      // Update the position in the store\n      setBposition(currentPosition);\n    }\n  }, [bannerSteps === null || bannerSteps === void 0 ? void 0 : (_bannerSteps$Canvas2 = bannerSteps.Canvas) === null || _bannerSteps$Canvas2 === void 0 ? void 0 : _bannerSteps$Canvas2.Position, setBposition]);\n  const renderHtmlSnippet = snippet => {\n    if (!snippet) return \"Sample ...\"; // Return an empty string if snippet is null or undefined.\n    return snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\n      return `${p1}${p2}\" target=\"_blank\"${p3}`;\n    });\n  };\n  const image = imageSrc;\n  const isBase64 = url => url.startsWith(\"data:image/\");\n  const textField = (bannerSteps === null || bannerSteps === void 0 ? void 0 : (_bannerSteps$TextFiel = bannerSteps.TextFieldProperties) === null || _bannerSteps$TextFiel === void 0 ? void 0 : _bannerSteps$TextFiel[0]) || {};\n  const {\n    Text: textFieldText,\n    Alignment,\n    Hyperlink,\n    Emoji,\n    TextProperties\n  } = textField;\n  const {\n    Bold,\n    Italic,\n    TextColor\n  } = TextProperties || {};\n  const customButton = (bannerSteps === null || bannerSteps === void 0 ? void 0 : (_bannerSteps$ButtonSe = bannerSteps.ButtonSection) === null || _bannerSteps$ButtonSe === void 0 ? void 0 : (_bannerSteps$ButtonSe2 = _bannerSteps$ButtonSe.map(section => section.CustomButtons.map(button => ({\n    ...button,\n    ContainerId: section.Id // Attach the container ID for grouping\n  })))) === null || _bannerSteps$ButtonSe2 === void 0 ? void 0 : _bannerSteps$ButtonSe2.reduce((acc, curr) => acc.concat(curr), [])) || [];\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleContinue = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handleButtonClick = action => {\n    if (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\n      window.open(action.TargetUrl);\n      //onContinue();\n    } else if (action.Action === \"start-interaction\") {\n      // onContinue();\n      // setOverlayValue(false);\n    } else if (action.Action === \"close\") {\n      // onClose();\n      // setOverlayValue(false);\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\") {\n        // Reset to the first step\n        setCurrentStep(1);\n      }\n    }\n  };\n  const designProps = (bannerSteps === null || bannerSteps === void 0 ? void 0 : bannerSteps.Design) || {};\n  const IconColor = designProps.IconColor || \"#000\";\n  const IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\n  const canvas = (bannerSteps === null || bannerSteps === void 0 ? void 0 : bannerSteps.Canvas) || {};\n  const BackgroundColor = (canvas === null || canvas === void 0 ? void 0 : canvas.BackgroundColor) || \"#f1f1f7\";\n  const BackgroundImage = (canvas === null || canvas === void 0 ? void 0 : canvas.BackgroundImage) || \"\";\n  const Width = canvas.Width || \"100%\";\n  const Radius = canvas.Radius || \"0\";\n  const Padding = canvas.Padding || \"10\";\n  const BorderSize = canvas.BorderSize || \"2\";\n  const BorderColor = canvas.BorderColor || \"#f1f1f7\";\n  const Position = \"absolute\";\n  const zindex = canvas.Zindex || \"999999\";\n  const Modal = bannerSteps === null || bannerSteps === void 0 ? void 0 : bannerSteps.Modal;\n  const isCloseDisabled = (_Modal$DismissOption = Modal === null || Modal === void 0 ? void 0 : Modal.DismissOption) !== null && _Modal$DismissOption !== void 0 ? _Modal$DismissOption : false;\n  const Design = (bannerSteps === null || bannerSteps === void 0 ? void 0 : bannerSteps.Design) || {};\n  const htmlSnippet = (bannerSteps === null || bannerSteps === void 0 ? void 0 : bannerSteps.HtmlSnippet) || \"\";\n\n  // Apply overflow hidden to body when canvas position is \"Cover Top\"\n  useEffect(() => {\n    if ((canvas === null || canvas === void 0 ? void 0 : canvas.Position) === \"Cover Top\") {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [canvas === null || canvas === void 0 ? void 0 : canvas.Position]); // Re-run when canvas position changes\n\n  const countLinesFromHtml = html => {\n    const paragraphCount = (html.match(/<p>/g) || []).length;\n    const brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\n    return paragraphCount > 0 ? paragraphCount - 1 : 0;\n  };\n\n  // const renderHtmlSnippet = (snippet: string) => {\n  // \treturn parse(snippet, {\n  // \t\treplace: (domNode: any) => {\n  // \t\t\tif (domNode.name === \"font\") {\n  // \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\n  // \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\n  // \t\t\t\treturn (\n  // \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\n  // \t\t\t\t\t\t{domToReact(domNode.children)}\n  // \t\t\t\t\t</span>\n  // \t\t\t\t);\n  // \t\t\t}\n  // \t\t\treturn undefined;\n  // \t\t},\n  // \t});\n  // };\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : (_savedGuideData$Guide3 = _savedGuideData$Guide2.Tooltip) === null || _savedGuideData$Guide3 === void 0 ? void 0 : _savedGuideData$Guide3.EnableProgress) || false;\n  let progressTop = 0;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6;\n    if (selectedOption === 1) {\n      progressTop = 10;\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      progressTop = 7;\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      progressTop = 7;\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      progressTop = 14;\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide4 = savedGuideData.GuideStep) === null || _savedGuideData$Guide4 === void 0 ? void 0 : (_savedGuideData$Guide5 = _savedGuideData$Guide4[0]) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5.Tooltip) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          padding: \"8px 0 0 0 !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '14px',\n            height: '4px',\n            backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0',\n            // Active color and inactive color\n            borderRadius: '100px'\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 23\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"12px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"calc(50% - 410px)\",\n          padding: \"8px 0 0 0\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      // position: \"relative\",\n      // top: \"55px\"\n    },\n    className: \"qadpt-container\",\n    children: showBanner && /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-boxpre\",\n      id: \"guide-popup\",\n      sx: {\n        // position: \"relative\",\n        // top: \"55px\",\n        // ...BannerWrapper,\n        //top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\n        left: \"50%\",\n        height: \"auto\",\n        marginTop: `${16 + parseInt(Padding || 0) + parseInt(BorderSize || 0) + (enableProgress ? progressTop : 0) + (customButton && customButton.length > 0 ? 4 : 0) + countLinesFromHtml(textFieldText) * 10}px`,\n        //\"29px\",\n        transform: \"translate(-50%, -50%)\",\n        backgroundColor: sectionColor,\n        //width: Width,\n        maxWidth: \"100%\",\n        //borderRadius: Radius,\n        padding: `${Padding}px`,\n        //borderWidth: \"2px\",\n        //border: `${BorderSize}px solid ${BorderColor}`,\n        boxShadow: Object.keys(canvas).length == 0 ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : (canvas === null || canvas === void 0 ? void 0 : canvas.Position) == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",\n        //(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\n        position: Position,\n        zIndex: zindex,\n        background: `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important`,\n        // border: `${BorderSize}px solid ${BorderColor}`,\n        borderTop: `${BorderSize}px solid ${BorderColor} !important`,\n        borderRight: `${BorderSize}px solid ${BorderColor} !important`,\n        borderLeft: `${BorderSize}px solid ${BorderColor} !important`,\n        borderBottom: `${BorderSize}px solid ${BorderColor} !important`\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-row\",\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            // margin: \"8px \",\n            //bottom: \"45px\",\n            position: \"relative\",\n            display: \"flex\",\n            alignItems: \"center\",\n            placeContent: \"center\",\n            width: \"100%\",\n            \"& .MuiTypography-root\": {\n              width: \"100%\",\n              margin: \"0\"\n            }\n          },\n          children: [Hyperlink ? /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"a\",\n            href: Hyperlink,\n            target: \"_blank\" // Open link in a new tab\n            ,\n            rel: \"noopener noreferrer\" // Security measure when using target=\"_blank\"\n            ,\n            sx: {\n              color: TextColor,\n              padding: \"5px 2px\",\n              textAlign: Alignment || \"center\",\n              marginTop: 1,\n              textDecoration: \"underline\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: renderHtmlSnippet(htmlSnippet)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 8\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-preview qadpt-rte\",\n            sx: {\n              color: TextColor,\n              textAlign: Alignment,\n              marginTop: 1,\n              padding: \"5px 2px\",\n              whiteSpace: \"pre-wrap\",\n              wordBreak: \"break-word\",\n              \"& p\": {\n                margin: \"0\"\n              }\n            },\n            dangerouslySetInnerHTML: {\n              __html: renderHtmlSnippet(textFieldText)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 8\n          }, this), Emoji && /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"span\",\n            sx: {\n              fontWeight: Bold ? \"bold\" : \"normal\",\n              fontStyle: Italic ? \"italic\" : \"normal\",\n              color: TextColor,\n              padding: \"5px 2px\",\n              textAlign: Alignment ? Alignment : \"center\",\n              mt: 1\n            },\n            children: Emoji\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 8\n          }, this), customButton && customButton.some(button => button.ButtonName && button.ButtonName.trim() !== \"\") && /*#__PURE__*/_jsxDEV(DialogActions, {\n            sx: {\n              justifyContent: \"center\",\n              padding: \"0 !important\",\n              height: \"40px\"\n            },\n            children: customButton.map((button, index) => {\n              var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5;\n              return /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => handleButtonClick(button.ButtonAction),\n                variant: \"contained\",\n                style: {\n                  backgroundColor: (_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor,\n                  color: (_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor,\n                  border: (_button$ButtonPropert3 = button.ButtonProperties) !== null && _button$ButtonPropert3 !== void 0 && _button$ButtonPropert3.ButtonBorderColor ? `2px solid ${button.ButtonProperties.ButtonBorderColor}` : \"none\",\n                  margin: \"0 5px\",\n                  fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || 15,\n                  width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                  padding: \"6px 16px\",\n                  textTransform: \"none\",\n                  borderRadius: \"20px\",\n                  lineHeight: \"22px\"\n                },\n                sx: {\n                  \"&:hover\": {\n                    filter: \"brightness(1.2)\"\n                  }\n                },\n                children: button.ButtonName\n              }, button.Id || `button-${index}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 11\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 9\n          }, this), Hyperlink && /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"a\",\n            href: Hyperlink,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              color: TextColor,\n              textAlign: Alignment || \"left\",\n              mt: 1,\n              textDecoration: \"underline\"\n            },\n            children: renderHtmlSnippet(textFieldText)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 8\n          }, this), image && /*#__PURE__*/_jsxDEV(Box, {\n            component: \"a\",\n            href: ((_initialGuideData$Gui2 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui2 === void 0 ? void 0 : (_initialGuideData$Gui3 = _initialGuideData$Gui2.ImageProperties) === null || _initialGuideData$Gui3 === void 0 ? void 0 : _initialGuideData$Gui3.Hyperlink) || \"#\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              textAlign: Alignment || \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: isBase64(image) ? image : image,\n              sx: {\n                maxHeight: ((_initialGuideData$Gui4 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui4 === void 0 ? void 0 : (_initialGuideData$Gui5 = _initialGuideData$Gui4.ImageProperties) === null || _initialGuideData$Gui5 === void 0 ? void 0 : _initialGuideData$Gui5.MaxImageHeight) || \"auto\",\n                objectFit: ((_initialGuideData$Gui6 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui6 === void 0 ? void 0 : (_initialGuideData$Gui7 = _initialGuideData$Gui6.ImageProperties) === null || _initialGuideData$Gui7 === void 0 ? void 0 : (_initialGuideData$Gui8 = _initialGuideData$Gui7.UploadedImages) === null || _initialGuideData$Gui8 === void 0 ? void 0 : (_initialGuideData$Gui9 = _initialGuideData$Gui8[0]) === null || _initialGuideData$Gui9 === void 0 ? void 0 : _initialGuideData$Gui9.Fit) || \"contain\",\n                display: \"block\",\n                margin: \"0 auto\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 6\n        }, this), \" \", isCloseDisabled && /*#__PURE__*/_jsxDEV(IconButton, {\n          sx: {\n            // position: \"fixed\",\n            boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\n            marginLeft: \"2px\",\n            background: \"#fff !important\",\n            border: \"1px solid #ccc\",\n            zIndex: \"999999\",\n            borderRadius: \"50px\",\n            padding: \"3px !important\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            sx: {\n              zoom: \"1\",\n              color: \"#000\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          ...(progressTemplate === \"linear\" && {\n            display: \"flex\",\n            placeContent: \"center\",\n            alignItems: \"center\"\n          })\n        },\n        children: totalSteps >= 1 && enableProgress ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: renderProgress()\n        }, void 0, false) : null\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 3\n  }, this);\n};\n_s(BannerStepPreview, \"K1swHUtmxkQ7oYOPixDYwrNte3E=\", false, function () {\n  return [useDrawerStore, useDrawerStore];\n});\n_c = BannerStepPreview;\nexport default BannerStepPreview;\nvar _c;\n$RefreshReg$(_c, \"BannerStepPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Typography", "IconButton", "DialogActions", "MobileStepper", "LinearProgress", "CloseIcon", "useDrawerStore", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BannerStepPreview", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "backgroundC", "totalSteps", "savedGuideData", "progress", "onClose", "_s", "_initialGuideData$Gui", "_bannerSteps$Canvas2", "_bannerSteps$TextFiel", "_bannerSteps$ButtonSe", "_bannerSteps$ButtonSe2", "_Modal$DismissOption", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_initialGuideData$Gui2", "_initialGuideData$Gui3", "_initialGuideData$Gui4", "_initialGuideData$Gui5", "_initialGuideData$Gui6", "_initialGuideData$Gui7", "_initialGuideData$Gui8", "_initialGuideData$Gui9", "btnBgColor", "btnTextColor", "btnBorderColor", "setCurrentStep", "currentStep", "selectedOption", "ProgressColor", "setBposition", "state", "showBanner", "setShowBanner", "setImageSrc", "imageSrc", "htmlContent", "sectionColor", "bannerSteps", "GuideStep", "_bannerSteps$Canvas", "currentPosition", "<PERSON><PERSON>", "Position", "renderHtmlSnippet", "snippet", "replace", "match", "p1", "p2", "p3", "image", "isBase64", "url", "startsWith", "textField", "TextFieldProperties", "Text", "textFieldText", "Alignment", "Hyperlink", "<PERSON><PERSON><PERSON>", "TextProperties", "Bold", "Italic", "TextColor", "customButton", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "handlePrevious", "handleContinue", "handleButtonClick", "action", "Action", "window", "open", "TargetUrl", "designProps", "Design", "IconColor", "IconOpacity", "QuietIcon", "canvas", "BackgroundColor", "BackgroundImage", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Padding", "BorderSize", "BorderColor", "zindex", "Zindex", "Modal", "isCloseDisabled", "DismissOption", "htmlSnippet", "HtmlSnippet", "document", "body", "style", "overflow", "countLinesFromHtml", "html", "paragraphCount", "length", "brCount", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "progressTop", "getProgressTemplate", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "steps", "position", "activeStep", "sx", "backgroundColor", "padding", "backButton", "visibility", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "children", "Array", "from", "_", "index", "width", "height", "borderRadius", "fontSize", "color", "value", "className", "id", "left", "marginTop", "parseInt", "transform", "max<PERSON><PERSON><PERSON>", "boxShadow", "Object", "keys", "zIndex", "background", "borderTop", "borderRight", "borderLeft", "borderBottom", "margin", "component", "href", "target", "rel", "textAlign", "textDecoration", "dangerouslySetInnerHTML", "__html", "whiteSpace", "wordBreak", "fontWeight", "fontStyle", "mt", "some", "ButtonName", "trim", "justifyContent", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "onClick", "ButtonAction", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "border", "ButtonBorderColor", "FontSize", "textTransform", "lineHeight", "filter", "ImageProperties", "src", "maxHeight", "MaxImageHeight", "objectFit", "UploadedImages", "Fit", "marginLeft", "zoom", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/tours/BannerStepPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Button, Typography, IconButton, DialogActions, MobileStepper, LinearProgress } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { CustomIconButton } from \"../Bannerspreview/Button\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\n\r\n\r\nconst BannerStepPreview = ({ showBannerenduser, setShowBannerenduser, initialGuideData, backgroundC ,totalSteps,savedGuideData,\r\n\tprogress, onClose}: any) => {\r\n\tconst {\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tsetCurrentStep,\r\n\t\tcurrentStep,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t\tsetBposition,\r\n\r\n\t} = useDrawerStore((state:any) => state);\r\n\tconst [showBanner, setShowBanner] = useState(true);\r\n\tconst { setImageSrc, imageSrc, htmlContent, sectionColor } = useDrawerStore((state: any) => state);\r\n\tconst bannerSteps = initialGuideData.GuideStep?.[currentStep - 1];\r\n\t// Update the position when the component mounts or when the canvas position changes\r\n\tuseEffect(() => {\r\n\t\t// Get the current Canvas position from the step\r\n\t\tconst currentPosition = bannerSteps?.Canvas?.Position;\r\n\t\tif (currentPosition) {\r\n\t\t\t// Update the position in the store\r\n\t\t\tsetBposition(currentPosition);\r\n\r\n\t\t}\r\n\t}, [bannerSteps?.Canvas?.Position, setBposition]);\r\n\tconst renderHtmlSnippet = (snippet: string | undefined | null) => {\r\n\t\tif (!snippet) return \"Sample ...\"; // Return an empty string if snippet is null or undefined.\r\n\t\treturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t});\r\n\t};\r\n\tconst image = imageSrc;\r\n\tconst isBase64 = (url: string) => url.startsWith(\"data:image/\");\r\n\tconst textField = bannerSteps?.TextFieldProperties?.[0] || {};\r\n\tconst { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;\r\n\tconst { Bold, Italic, TextColor } = TextProperties || {};\r\n\tconst customButton =\r\n    bannerSteps?.ButtonSection\r\n        ?.map((section: any) =>\r\n            section.CustomButtons.map((button: any) => ({\r\n                ...button,\r\n                ContainerId: section.Id, // Attach the container ID for grouping\r\n            }))\r\n        )\r\n\t\t\t?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [];\r\n\r\n\t\tconst handlePrevious = () => {\r\n\t\t\tif (currentStep > 1) {\r\n\t\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\t}\r\n\t\t};\r\n\tconst handleContinue = () =>\r\n\t{\r\n\t\tif (currentStep < totalSteps) {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\r\n\r\n\r\n\t\t}\r\n\t\t}\r\n\tconst handleButtonClick = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\r\n\t\t\twindow.open(action.TargetUrl);\r\n\t\t\t//onContinue();\r\n\t\t} else if (action.Action === \"start-interaction\") {\r\n\t\t\t// onContinue();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"close\") {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tif (action.Action == \"Previous\" || action.Action == \"previous\") {\r\n                handlePrevious();\r\n            }\r\n            else if (action.Action == \"Next\" || action.Action == \"next\") {\r\n                handleContinue();\r\n            }\r\n            else if (action.Action == \"Restart\") {\r\n                // Reset to the first step\r\n                setCurrentStep(1);\r\n            }\r\n\t\t}\r\n\t};\r\n\tconst designProps = bannerSteps?.Design || {};\r\n\tconst IconColor = designProps.IconColor || \"#000\";\r\n\tconst IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\r\n\tconst canvas = bannerSteps?.Canvas || {};\r\n\tconst BackgroundColor = canvas?.BackgroundColor || \"#f1f1f7\";\r\n\tconst BackgroundImage = canvas?.BackgroundImage || \"\";\r\n\tconst Width = canvas.Width || \"100%\";\r\n\tconst Radius = canvas.Radius || \"0\";\r\n\tconst Padding = canvas.Padding || \"10\";\r\n\tconst BorderSize = canvas.BorderSize || \"2\";\r\n\tconst BorderColor = canvas.BorderColor || \"#f1f1f7\";\r\n\tconst Position = \"absolute\";\r\n\tconst zindex = canvas.Zindex || \"999999\";\r\n\tconst Modal = bannerSteps?.Modal;\r\n\tconst isCloseDisabled = Modal?.DismissOption ?? false;\r\n\tconst Design = bannerSteps?.Design || {};\r\n\tconst htmlSnippet = bannerSteps?.HtmlSnippet || \"\";\r\n\r\n\t// Apply overflow hidden to body when canvas position is \"Cover Top\"\r\n\tuseEffect(() => {\r\n\t\tif (canvas?.Position === \"Cover Top\") {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t} else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t}\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n\t}, [canvas?.Position]); // Re-run when canvas position changes\r\n\r\n\tconst countLinesFromHtml = (html: string) => {\r\n\t\tconst paragraphCount = (html.match(/<p>/g) || []).length;\r\n\r\n\t\tconst brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\r\n\r\n\t\treturn paragraphCount>0 ? paragraphCount-1 : 0;\r\n\t};\r\n\r\n\t// const renderHtmlSnippet = (snippet: string) => {\r\n\t// \treturn parse(snippet, {\r\n\t// \t\treplace: (domNode: any) => {\r\n\t// \t\t\tif (domNode.name === \"font\") {\r\n\t// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\r\n\t// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\r\n\t// \t\t\t\treturn (\r\n\t// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\r\n\t// \t\t\t\t\t\t{domToReact(domNode.children)}\r\n\t// \t\t\t\t\t</span>\r\n\t// \t\t\t\t);\r\n\t// \t\t\t}\r\n\t// \t\t\treturn undefined;\r\n\t// \t\t},\r\n\t// \t});\r\n\t// };\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\tlet progressTop = 0;\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\tprogressTop = 10;\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\tprogressTop = 7;\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\tprogressTop = 7;\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\tprogressTop = 14;\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\", padding:\"8px 0 0 0 !important\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: totalSteps }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{fontSize:\"12px\", color: ProgressColor}}>\r\n\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{    width: \"calc(50% - 410px)\",\r\n\t\t\t\tpadding: \"8px 0 0 0\"}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n                            sx={{'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\t// position: \"relative\",\r\n\t\t\t// top: \"55px\"\r\n\t\t}} className=\"qadpt-container\">\r\n\t\t\t{showBanner && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-boxpre\"\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t// position: \"relative\",\r\n\t\t\t\t\t\t// top: \"55px\",\r\n\t\t\t\t\t\t// ...BannerWrapper,\r\n\t\t\t\t\t\t//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\r\n\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmarginTop: `${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(enableProgress ? progressTop : 0)+(customButton && customButton.length > 0? 4 : 0)+(countLinesFromHtml(textFieldText)*10)}px`,//\"29px\",\r\n\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor,\r\n\t\t\t\t\t\t//width: Width,\r\n\t\t\t\t\t\tmaxWidth:\"100%\",\r\n\t\t\t\t\t\t//borderRadius: Radius,\r\n\t\t\t\t\t\tpadding: `${Padding}px`,\r\n\t\t\t\t\t\t//borderWidth: \"2px\",\r\n\t\t\t\t\t\t//border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tboxShadow: Object.keys(canvas).length == 0? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : canvas?.Position == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\r\n\t\t\t\t\t\tposition: Position,\r\n\t\t\t\t\t\tzIndex: zindex,\r\n\r\n\t\t\t\t\t\tbackground:  `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important` ,\r\n\t\t\t\t\t\t// border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t  `${BorderSize}px solid ${BorderColor} !important`,\r\n\r\n\t\t\t\t\tborderBottom: `${BorderSize}px solid ${BorderColor} !important`,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-row\" sx={{  display: \"flex\", alignItems: \"center\"}}>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// margin: \"8px \",\r\n\t\t\t\t\t\t\t//bottom: \"45px\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\r\n\r\n\t\t\t\t\t\t\t\"& .MuiTypography-root\": {\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t},\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Display hyperlink  */}\r\n\t\t\t\t\t\t{Hyperlink ? (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\" // Open link in a new tab\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\" // Security measure when using target=\"_blank\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(htmlSnippet) }}\r\n\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<Typography \t\t\t\t\t\t\t\t  className=\"qadpt-preview qadpt-rte\"\r\n\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment,\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\"& p\": {\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t\t\t  },\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textFieldText) }}\r\n\t\t\t\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{Emoji && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontWeight: Bold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tfontStyle: Italic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment ? Alignment : \"center\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{Emoji}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{customButton &&\r\n\t\t\t\t\t\t\tcustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== \"\") && (\r\n\t\t\t\t\t\t\t\t<DialogActions\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{customButton.map((button: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tkey={button.Id || `button-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${button.ButtonProperties.ButtonBorderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || 15,\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"22px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilter: \"brightness(1.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</DialogActions>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{/* {CustomButton && CustomButton.ButtonName && (\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontSize: CustomButton.ButtonProperties?.FontSize || 14,\r\n\t\t\t\t\t\t\t\t\twidth: CustomButton.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: CustomButton.ButtonProperties?.Padding || \"10px\",\r\n\t\t\t\t\t\t\t\t\tcolor: CustomButton.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: CustomButton.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t//textAlign: CustomButton.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t//display: \"block\",\r\n\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"0px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\tmx: CustomButton.Alignment === \"center\" ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={handleButtonClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{CustomButton.ButtonName}\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t)} */}\r\n\r\n\t\t\t\t\t\t{Hyperlink && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"left\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{renderHtmlSnippet(textFieldText)}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{image && (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={initialGuideData.GuideStep[0]?.ImageProperties?.Hyperlink || \"#\"}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{ textAlign: Alignment || \"center\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc={isBase64(image) ? image : image}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tmaxHeight: initialGuideData.GuideStep[0]?.ImageProperties?.MaxImageHeight || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tobjectFit: initialGuideData.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>{\" \"}\r\n\t\t\t\t\t{isCloseDisabled && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n\t\t\t\t\tmarginLeft: \"2px\",\r\n\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\tzIndex:\"999999\",\r\n\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\tpadding:\"3px !important\"\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\"}}   />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t<Box sx={{\r\n  ...(progressTemplate === \"linear\" && {\r\n\tdisplay: \"flex\",\r\n\tplaceContent: \"center\",\r\n\talignItems:\"center\"\r\n  }),\r\n}}>\r\n\t\t{totalSteps >= 1 && enableProgress ? (\r\n                    <>\r\n                        {renderProgress()}\r\n                    </>\r\n                ) : (\r\n                    null\r\n                )}\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t)}\r\n\r\n\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nexport default BannerStepPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc,QAAQ,eAAe;AACjH,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGrD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC,oBAAoB;EAAEC,gBAAgB;EAAEC,WAAW;EAAEC,UAAU;EAACC,cAAc;EAC7HC,QAAQ;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5B,MAAM;IACLC,UAAU;IACVC,YAAY;IACZC,cAAc;IACdC,cAAc;IACdC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC;EAED,CAAC,GAAGvC,cAAc,CAAEwC,KAAS,IAAKA,KAAK,CAAC;EACxC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM;IAAEqD,WAAW;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAG9C,cAAc,CAAEwC,KAAU,IAAKA,KAAK,CAAC;EAClG,MAAMO,WAAW,IAAAhC,qBAAA,GAAGP,gBAAgB,CAACwC,SAAS,cAAAjC,qBAAA,uBAA1BA,qBAAA,CAA6BqB,WAAW,GAAG,CAAC,CAAC;EACjE;EACA7C,SAAS,CAAC,MAAM;IAAA,IAAA0D,mBAAA;IACf;IACA,MAAMC,eAAe,GAAGH,WAAW,aAAXA,WAAW,wBAAAE,mBAAA,GAAXF,WAAW,CAAEI,MAAM,cAAAF,mBAAA,uBAAnBA,mBAAA,CAAqBG,QAAQ;IACrD,IAAIF,eAAe,EAAE;MACpB;MACAX,YAAY,CAACW,eAAe,CAAC;IAE9B;EACD,CAAC,EAAE,CAACH,WAAW,aAAXA,WAAW,wBAAA/B,oBAAA,GAAX+B,WAAW,CAAEI,MAAM,cAAAnC,oBAAA,uBAAnBA,oBAAA,CAAqBoC,QAAQ,EAAEb,YAAY,CAAC,CAAC;EACjD,MAAMc,iBAAiB,GAAIC,OAAkC,IAAK;IACjE,IAAI,CAACA,OAAO,EAAE,OAAO,YAAY,CAAC,CAAC;IACnC,OAAOA,OAAO,CAACC,OAAO,CAAC,qCAAqC,EAAE,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MACpF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;IAC1C,CAAC,CAAC;EACH,CAAC;EACD,MAAMC,KAAK,GAAGhB,QAAQ;EACtB,MAAMiB,QAAQ,GAAIC,GAAW,IAAKA,GAAG,CAACC,UAAU,CAAC,aAAa,CAAC;EAC/D,MAAMC,SAAS,GAAG,CAAAjB,WAAW,aAAXA,WAAW,wBAAA9B,qBAAA,GAAX8B,WAAW,CAAEkB,mBAAmB,cAAAhD,qBAAA,uBAAhCA,qBAAA,CAAmC,CAAC,CAAC,KAAI,CAAC,CAAC;EAC7D,MAAM;IAAEiD,IAAI,EAAEC,aAAa;IAAEC,SAAS;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAGP,SAAS;EACtF,MAAM;IAAEQ,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGH,cAAc,IAAI,CAAC,CAAC;EACxD,MAAMI,YAAY,GACf,CAAA5B,WAAW,aAAXA,WAAW,wBAAA7B,qBAAA,GAAX6B,WAAW,CAAE6B,aAAa,cAAA1D,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CACM2D,GAAG,CAAEC,OAAY,IACfA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;IACxC,GAAGA,MAAM;IACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;EAC7B,CAAC,CAAC,CACN,CAAC,cAAA/D,sBAAA,uBANLA,sBAAA,CAOCgE,MAAM,CAAC,CAACC,GAAU,EAAEC,IAAW,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EAAE;EAElE,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAInD,WAAW,GAAG,CAAC,EAAE;MACpBD,cAAc,CAACC,WAAW,GAAG,CAAC,CAAC;IAChC;EACD,CAAC;EACF,MAAMoD,cAAc,GAAGA,CAAA,KACvB;IACC,IAAIpD,WAAW,GAAG1B,UAAU,EAAE;MAC7ByB,cAAc,CAACC,WAAW,GAAG,CAAC,CAAC;IAIhC;EACA,CAAC;EACF,MAAMqD,iBAAiB,GAAIC,MAAW,IAAK;IAC1C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,EAAE;MAC5FC,MAAM,CAACC,IAAI,CAACH,MAAM,CAACI,SAAS,CAAC;MAC7B;IACD,CAAC,MAAM,IAAIJ,MAAM,CAACC,MAAM,KAAK,mBAAmB,EAAE;MACjD;MACA;IAAA,CACA,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,OAAO,EAAE;MACrC;MACA;IAAA,CACA,MAED;MACC,IAAID,MAAM,CAACC,MAAM,IAAI,UAAU,IAAID,MAAM,CAACC,MAAM,IAAI,UAAU,EAAE;QACnDJ,cAAc,CAAC,CAAC;MACpB,CAAC,MACI,IAAIG,MAAM,CAACC,MAAM,IAAI,MAAM,IAAID,MAAM,CAACC,MAAM,IAAI,MAAM,EAAE;QACzDH,cAAc,CAAC,CAAC;MACpB,CAAC,MACI,IAAIE,MAAM,CAACC,MAAM,IAAI,SAAS,EAAE;QACjC;QACAxD,cAAc,CAAC,CAAC,CAAC;MACrB;IACV;EACD,CAAC;EACD,MAAM4D,WAAW,GAAG,CAAAhD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiD,MAAM,KAAI,CAAC,CAAC;EAC7C,MAAMC,SAAS,GAAGF,WAAW,CAACE,SAAS,IAAI,MAAM;EACjD,MAAMC,WAAW,GAAGH,WAAW,CAACI,SAAS,GAAG,GAAG,GAAG,GAAG;EACrD,MAAMC,MAAM,GAAG,CAAArD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,MAAM,KAAI,CAAC,CAAC;EACxC,MAAMkD,eAAe,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,eAAe,KAAI,SAAS;EAC5D,MAAMC,eAAe,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,eAAe,KAAI,EAAE;EACrD,MAAMC,KAAK,GAAGH,MAAM,CAACG,KAAK,IAAI,MAAM;EACpC,MAAMC,MAAM,GAAGJ,MAAM,CAACI,MAAM,IAAI,GAAG;EACnC,MAAMC,OAAO,GAAGL,MAAM,CAACK,OAAO,IAAI,IAAI;EACtC,MAAMC,UAAU,GAAGN,MAAM,CAACM,UAAU,IAAI,GAAG;EAC3C,MAAMC,WAAW,GAAGP,MAAM,CAACO,WAAW,IAAI,SAAS;EACnD,MAAMvD,QAAQ,GAAG,UAAU;EAC3B,MAAMwD,MAAM,GAAGR,MAAM,CAACS,MAAM,IAAI,QAAQ;EACxC,MAAMC,KAAK,GAAG/D,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+D,KAAK;EAChC,MAAMC,eAAe,IAAA3F,oBAAA,GAAG0F,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,aAAa,cAAA5F,oBAAA,cAAAA,oBAAA,GAAI,KAAK;EACrD,MAAM4E,MAAM,GAAG,CAAAjD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiD,MAAM,KAAI,CAAC,CAAC;EACxC,MAAMiB,WAAW,GAAG,CAAAlE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmE,WAAW,KAAI,EAAE;;EAElD;EACA3H,SAAS,CAAC,MAAM;IACf,IAAI,CAAA6G,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhD,QAAQ,MAAK,WAAW,EAAE;MACrC+D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACxC,CAAC,MAAM;MACNH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC;;IAEA;IACA,OAAO,MAAM;MACZH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC,CAAC;EACF,CAAC,EAAE,CAAClB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhD,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExB,MAAMmE,kBAAkB,GAAIC,IAAY,IAAK;IAC5C,MAAMC,cAAc,GAAG,CAACD,IAAI,CAAChE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAEkE,MAAM;IAExD,MAAMC,OAAO,GAAG,CAACH,IAAI,CAAChE,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAEkE,MAAM;IAExD,OAAOD,cAAc,GAAC,CAAC,GAAGA,cAAc,GAAC,CAAC,GAAG,CAAC;EAC/C,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMG,cAAc,GAAG,CAAAjH,cAAc,aAAdA,cAAc,wBAAAU,qBAAA,GAAdV,cAAc,CAAEqC,SAAS,cAAA3B,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCuG,OAAO,cAAAtG,sBAAA,uBAAvCA,sBAAA,CAAyCuG,cAAc,KAAI,KAAK;EACvF,IAAIC,WAAW,GAAG,CAAC;EACnB,SAASC,mBAAmBA,CAAC3F,cAAmB,EAAE;IAAA,IAAA4F,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjD,IAAI9F,cAAc,KAAK,CAAC,EAAE;MACzB0F,WAAW,GAAG,EAAE;MAChB,OAAO,MAAM;IACd,CAAC,MAAM,IAAI1F,cAAc,KAAK,CAAC,EAAE;MAChC0F,WAAW,GAAG,CAAC;MACf,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAI1F,cAAc,KAAK,CAAC,EAAE;MAChC0F,WAAW,GAAG,CAAC;MACf,OAAO,aAAa;IACrB,CAAC,MACU,IAAI1F,cAAc,KAAK,CAAC,EAAE;MACpC0F,WAAW,GAAG,EAAE;MAChB,OAAO,aAAa;IACrB;IAEA,OAAO,CAAApH,cAAc,aAAdA,cAAc,wBAAAsH,sBAAA,GAAdtH,cAAc,CAAEqC,SAAS,cAAAiF,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCL,OAAO,cAAAM,sBAAA,uBAAvCA,sBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAAC3F,cAAc,CAAC;EAC5D,MAAMiG,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACV,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIS,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCnI,OAAA,CAACL,aAAa;QACb0I,OAAO,EAAC,MAAM;QACdC,KAAK,EAAE9H,UAAW;QAClB+H,QAAQ,EAAC,QAAQ;QACjBC,UAAU,EAAEtG,WAAW,GAAG,CAAE;QAC5BuG,EAAE,EAAE;UAAEC,eAAe,EAAE,aAAa;UAAEC,OAAO,EAAC,sBAAsB;UAAG,+BAA+B,EAAE;YACrFD,eAAe,EAAEtG,aAAa,CAAE;UAClC;QAAG,CAAE;QACtBwG,UAAU,eAAE5I,OAAA,CAACT,MAAM;UAAC4H,KAAK,EAAE;YAAE0B,UAAU,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDC,UAAU,eAAElJ,OAAA,CAACT,MAAM;UAAC4H,KAAK,EAAE;YAAE0B,UAAU,EAAE;UAAS;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACM,IAAId,gBAAgB,KAAK,aAAa,EAAE;MAC7C,oBACanI,OAAA,CAACV,GAAG;QAACmJ,EAAE,EAAE;UAACE,OAAO,EAAC,sBAAsB;UAACQ,OAAO,EAAE,MAAM;UACnEC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,QAAQ;UACtBC,GAAG,EAAE;QAAK,CAAE;QAAAC,QAAA,EAGIC,KAAK,CAACC,IAAI,CAAC;UAAEjC,MAAM,EAAEhH;QAAW,CAAC,CAAC,CAACmE,GAAG,CAAC,CAAC+E,CAAC,EAAEC,KAAK,kBAC/C3J,OAAA;UAEEmH,KAAK,EAAE;YACLyC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACbnB,eAAe,EAAEiB,KAAK,KAAKzH,WAAW,GAAG,CAAC,GAAGE,aAAa,GAAG,SAAS;YAAE;YACxE0H,YAAY,EAAE;UAChB;QAAE,GANGH,KAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAEpB;IACA,IAAId,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCnI,OAAA,CAACV,GAAG;QAACmJ,EAAE,EAAE;UAACE,OAAO,EAAC,sBAAsB;UAACQ,OAAO,EAAE,MAAM;UACxDC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QACd,CAAE;QAAAE,QAAA,eACDvJ,OAAA,CAACR,UAAU;UAACiJ,EAAE,EAAE;YAACsB,QAAQ,EAAC,MAAM;YAAEC,KAAK,EAAE5H;UAAa,CAAE;UAAAmH,QAAA,GAAC,OACpD,EAACrH,WAAW,EAAC,MAAI,EAAC1B,UAAU;QAAA;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAId,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCnI,OAAA,CAACV,GAAG;QAACmJ,EAAE,EAAE;UAAKmB,KAAK,EAAE,mBAAmB;UACxCjB,OAAO,EAAE;QAAW,CAAE;QAAAY,QAAA,eACrBvJ,OAAA,CAACR,UAAU;UAAC6I,OAAO,EAAC,OAAO;UAAAkB,QAAA,eAC1BvJ,OAAA,CAACJ,cAAc;YACdyI,OAAO,EAAC,aAAa;YACrB4B,KAAK,EAAEvJ,QAAS;YACK+H,EAAE,EAAE;cAAC,0BAA0B,EAAE;gBAC7BC,eAAe,EAAEtG,aAAa,CAAE;cAClC;YAAE;UAAE;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACCjJ,OAAA,CAACV,GAAG;IAACmJ,EAAE,EAAE;MACR;MACA;IAAA,CACC;IAACyB,SAAS,EAAC,iBAAiB;IAAAX,QAAA,EAC5BhH,UAAU,iBACVvC,OAAA,CAACV,GAAG;MACH4K,SAAS,EAAC,cAAc;MACxBC,EAAE,EAAC,aAAa;MAChB1B,EAAE,EAAE;QACH;QACA;QACA;QACA;QACA2B,IAAI,EAAE,KAAK;QACXP,MAAM,EAAE,MAAM;QACdQ,SAAS,EAAE,GAAG,EAAE,GAACC,QAAQ,CAAC/D,OAAO,IAAE,CAAC,CAAC,GAAC+D,QAAQ,CAAC9D,UAAU,IAAE,CAAC,CAAC,IAAEkB,cAAc,GAAGG,WAAW,GAAG,CAAC,CAAC,IAAEpD,YAAY,IAAIA,YAAY,CAAC+C,MAAM,GAAG,CAAC,GAAE,CAAC,GAAG,CAAC,CAAC,GAAEH,kBAAkB,CAACpD,aAAa,CAAC,GAAC,EAAG,IAAI;QAAC;QAC7LsG,SAAS,EAAE,uBAAuB;QAClC7B,eAAe,EAAE9F,YAAY;QAC7B;QACA4H,QAAQ,EAAC,MAAM;QACf;QACA7B,OAAO,EAAE,GAAGpC,OAAO,IAAI;QACvB;QACA;QACAkE,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACzE,MAAM,CAAC,CAACsB,MAAM,IAAI,CAAC,GAAE,iCAAiC,GAAG,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhD,QAAQ,KAAI,WAAW,GAAG,iCAAiC,GAAG,MAAM;QAAC;QAC7JqF,QAAQ,EAAErF,QAAQ;QAClB0H,MAAM,EAAElE,MAAM;QAEdmE,UAAU,EAAG,GAAG1E,eAAe,GAAGA,eAAe,GAAG,SAAS,aAAa;QAC1E;QACA2E,SAAS,EACP,GAAGtE,UAAU,YAAYC,WAAW,aAAa;QAEjDsE,WAAW,EACV,GAAGvE,UAAU,YAAYC,WAAW,aAAa;QAElDuE,UAAU,EACV,GAAGxE,UAAU,YAAYC,WAAW,aAAa;QAEpDwE,YAAY,EAAE,GAAGzE,UAAU,YAAYC,WAAW;MAClD,CAAE;MAAA8C,QAAA,gBAEFvJ,OAAA,CAACV,GAAG;QAAC4K,SAAS,EAAC,WAAW;QAACzB,EAAE,EAAE;UAAGU,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAG,QAAA,gBACzEvJ,OAAA,CAACV,GAAG;UACHmJ,EAAE,EAAE;YACH;YACA;YACAF,QAAQ,EAAE,UAAU;YACpBY,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,YAAY,EAAE,QAAQ;YACrBO,KAAK,EAAE,MAAM;YAGd,uBAAuB,EAAE;cACxBA,KAAK,EAAE,MAAM;cACbsB,MAAM,EAAE;YACT;UAED,CAAE;UAAA3B,QAAA,GAGDpF,SAAS,gBACTnE,OAAA,CAACR,UAAU;YACV2L,SAAS,EAAC,GAAG;YACbC,IAAI,EAAEjH,SAAU;YAChBkH,MAAM,EAAC,QAAQ,CAAC;YAAA;YAChBC,GAAG,EAAC,qBAAqB,CAAC;YAAA;YAC1B7C,EAAE,EAAE;cACHuB,KAAK,EAAExF,SAAS;cAChBmE,OAAO,EAAE,SAAS;cAClB4C,SAAS,EAAErH,SAAS,IAAI,QAAQ;cAChCmG,SAAS,EAAE,CAAC;cACZmB,cAAc,EAAE;YACjB,CAAE;YACFC,uBAAuB,EAAE;cAAEC,MAAM,EAAEvI,iBAAiB,CAAC4D,WAAW;YAAE;UAAE;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,gBAEdjJ,OAAA,CAACR,UAAU;YAAW0K,SAAS,EAAC,yBAAyB;YAExDzB,EAAE,EAAE;cACHuB,KAAK,EAAExF,SAAS;cAChB+G,SAAS,EAAErH,SAAS;cACpBmG,SAAS,EAAE,CAAC;cACZ1B,OAAO,EAAE,SAAS;cAClBgD,UAAU,EAAE,UAAU;cACtBC,SAAS,EAAE,YAAY;cACvB,KAAK,EAAE;gBACNV,MAAM,EAAE;cACP;YACH,CAAE;YACFO,uBAAuB,EAAE;cAAEC,MAAM,EAAEvI,iBAAiB,CAACc,aAAa;YAAE;UAAE;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAChB,EACA7E,KAAK,iBACLpE,OAAA,CAACR,UAAU;YACV2L,SAAS,EAAC,MAAM;YAChB1C,EAAE,EAAE;cACHoD,UAAU,EAAEvH,IAAI,GAAG,MAAM,GAAG,QAAQ;cACpCwH,SAAS,EAAEvH,MAAM,GAAG,QAAQ,GAAG,QAAQ;cACvCyF,KAAK,EAAExF,SAAS;cAChBmE,OAAO,EAAE,SAAS;cAClB4C,SAAS,EAAErH,SAAS,GAAGA,SAAS,GAAG,QAAQ;cAC3C6H,EAAE,EAAE;YACL,CAAE;YAAAxC,QAAA,EAEDnF;UAAK;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACZ,EACAxE,YAAY,IACZA,YAAY,CAACuH,IAAI,CAAElH,MAAW,IAAKA,MAAM,CAACmH,UAAU,IAAInH,MAAM,CAACmH,UAAU,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,iBACvFlM,OAAA,CAACN,aAAa;YACb+I,EAAE,EAAE;cACJ0D,cAAc,EAAE,QAAQ;cACvBxD,OAAO,EAAE,cAAc;cACxBkB,MAAM,EAAE;YACR,CAAE;YAAAN,QAAA,EAED9E,YAAY,CAACE,GAAG,CAAC,CAACG,MAAW,EAAE6E,KAAU;cAAA,IAAAyC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACzCxM,OAAA,CAACT,MAAM;gBAENkN,OAAO,EAAEA,CAAA,KAAMlH,iBAAiB,CAACT,MAAM,CAAC4H,YAAY,CAAE;gBACtDrE,OAAO,EAAC,WAAW;gBACnBlB,KAAK,EAAE;kBACNuB,eAAe,GAAA0D,qBAAA,GAAEtH,MAAM,CAAC6H,gBAAgB,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBQ,qBAAqB;kBAC/D5C,KAAK,GAAAqC,sBAAA,GAAEvH,MAAM,CAAC6H,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBQ,eAAe;kBAC/CC,MAAM,EAAE,CAAAR,sBAAA,GAAAxH,MAAM,CAAC6H,gBAAgB,cAAAL,sBAAA,eAAvBA,sBAAA,CAAyBS,iBAAiB,GAC/C,aAAajI,MAAM,CAAC6H,gBAAgB,CAACI,iBAAiB,EAAE,GACxD,MAAM;kBACT7B,MAAM,EAAE,OAAO;kBACfnB,QAAQ,EAAE,EAAAwC,sBAAA,GAAAzH,MAAM,CAAC6H,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBS,QAAQ,KAAI,EAAE;kBACjDpD,KAAK,EAAE,EAAA4C,sBAAA,GAAA1H,MAAM,CAAC6H,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBnG,KAAK,KAAI,MAAM;kBAC/CsC,OAAO,EAAE,UAAU;kBACnBsE,aAAa,EAAE,MAAM;kBACrBnD,YAAY,EAAE,MAAM;kBACpBoD,UAAU,EAAE;gBACb,CAAE;gBACFzE,EAAE,EAAE;kBACH,SAAS,EAAE;oBACV0E,MAAM,EAAE;kBACT;gBACD,CAAE;gBAAA5D,QAAA,EAEDzE,MAAM,CAACmH;cAAU,GAvBbnH,MAAM,CAACE,EAAE,IAAI,UAAU2E,KAAK,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwB5B,CAAC;YAAA,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CACf,EAwBD9E,SAAS,iBACTnE,OAAA,CAACR,UAAU;YACV2L,SAAS,EAAC,GAAG;YACbC,IAAI,EAAEjH,SAAU;YAChBkH,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB7C,EAAE,EAAE;cACHuB,KAAK,EAAExF,SAAS;cAChB+G,SAAS,EAAErH,SAAS,IAAI,MAAM;cAC9B6H,EAAE,EAAE,CAAC;cACLP,cAAc,EAAE;YACjB,CAAE;YAAAjC,QAAA,EAEDpG,iBAAiB,CAACc,aAAa;UAAC;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACZ,EAEAvF,KAAK,iBACL1D,OAAA,CAACV,GAAG;YACH6L,SAAS,EAAC,GAAG;YACbC,IAAI,EAAE,EAAA9J,sBAAA,GAAAhB,gBAAgB,CAACwC,SAAS,CAAC,CAAC,CAAC,cAAAxB,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B8L,eAAe,cAAA7L,sBAAA,uBAA9CA,sBAAA,CAAgD4C,SAAS,KAAI,GAAI;YACvEkH,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzB7C,EAAE,EAAE;cAAE8C,SAAS,EAAErH,SAAS,IAAI;YAAS,CAAE;YAAAqF,QAAA,eAEzCvJ,OAAA,CAACV,GAAG;cACH6L,SAAS,EAAC,KAAK;cACfkC,GAAG,EAAE1J,QAAQ,CAACD,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAM;cACrC+E,EAAE,EAAE;gBACH6E,SAAS,EAAE,EAAA9L,sBAAA,GAAAlB,gBAAgB,CAACwC,SAAS,CAAC,CAAC,CAAC,cAAAtB,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B4L,eAAe,cAAA3L,sBAAA,uBAA9CA,sBAAA,CAAgD8L,cAAc,KAAI,MAAM;gBACnFC,SAAS,EAAE,EAAA9L,sBAAA,GAAApB,gBAAgB,CAACwC,SAAS,CAAC,CAAC,CAAC,cAAApB,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B0L,eAAe,cAAAzL,sBAAA,wBAAAC,sBAAA,GAA9CD,sBAAA,CAAgD8L,cAAc,cAAA7L,sBAAA,wBAAAC,sBAAA,GAA9DD,sBAAA,CAAiE,CAAC,CAAC,cAAAC,sBAAA,uBAAnEA,sBAAA,CAAqE6L,GAAG,KAAI,SAAS;gBAChGvE,OAAO,EAAE,OAAO;gBAChB+B,MAAM,EAAE;cACT;YAAE;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAAC,GAAG,EACTpC,eAAe,iBACf7G,OAAA,CAACP,UAAU;UACXgJ,EAAE,EAAE;YACH;YACFgC,SAAS,EAAE,iCAAiC;YAC5CkD,UAAU,EAAE,KAAK;YACjB9C,UAAU,EAAE,iBAAiB;YAC7BiC,MAAM,EAAE,gBAAgB;YACxBlC,MAAM,EAAC,QAAQ;YACdd,YAAY,EAAE,MAAM;YACrBnB,OAAO,EAAC;UAEP,CAAE;UAAAY,QAAA,eAEDvJ,OAAA,CAACH,SAAS;YAAE4I,EAAE,EAAE;cAACmF,IAAI,EAAC,GAAG;cAAC5D,KAAK,EAAC;YAAM;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAENjJ,OAAA,CAACV,GAAG;QAACmJ,EAAE,EAAE;UACZ,IAAIN,gBAAgB,KAAK,QAAQ,IAAI;YACtCgB,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE,QAAQ;YACtBD,UAAU,EAAC;UACV,CAAC;QACH,CAAE;QAAAG,QAAA,EACC/I,UAAU,IAAI,CAAC,IAAIkH,cAAc,gBAChB1H,OAAA,CAAAE,SAAA;UAAAqJ,QAAA,EACKnB,cAAc,CAAC;QAAC,gBACnB,CAAC,GAEH;MACH;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAED;EAEL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGG,CAAC;AAER,CAAC;AAACrI,EAAA,CAxeIT,iBAAiB;EAAA,QAYlBL,cAAc,EAE2CA,cAAc;AAAA;AAAA+N,EAAA,GAdtE1N,iBAAiB;AA0evB,eAAeA,iBAAiB;AAAC,IAAA0N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
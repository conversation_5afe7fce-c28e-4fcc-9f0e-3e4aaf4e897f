{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Bannerspreview\\\\Banner.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Button, Typography, IconButton, DialogActions } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BannerEndUser = ({\n  showBannerenduser,\n  setShowBannerenduser,\n  initialGuideData,\n  backgroundC,\n  currentStep\n}) => {\n  _s();\n  var _initialGuideData$Gui, _initialGuideData$Gui2, _initialGuideData$Gui3, _initialGuideData$Gui4, _initialGuideData$Gui5, _initialGuideData$Gui6, _initialGuideData$Gui7, _initialGuideData$Gui8, _initialGuideData$Gui9, _initialGuideData$Gui13, _initialGuideData$Gui14, _initialGuideData$Gui15, _initialGuideData$Gui16, _initialGuideData$Gui17, _initialGuideData$Gui18, _Modal$DismissOption, _initialGuideData$Gui19, _initialGuideData$Gui20, _initialGuideData$Gui21, _initialGuideData$Gui22, _initialGuideData$Gui23, _initialGuideData$Gui24, _initialGuideData$Gui25, _initialGuideData$Gui26, _initialGuideData$Gui27, _initialGuideData$Gui28, _initialGuideData$Gui29, _initialGuideData$Gui30;\n  const {\n    btnBgColor,\n    btnTextColor,\n    btnBorderColor\n  } = useDrawerStore(state => state);\n  const [showBanner, setShowBanner] = useState(true);\n  const {\n    setImageSrc,\n    imageSrc,\n    htmlContent,\n    sectionColor\n  } = useDrawerStore(state => state);\n  const Teext = initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui = initialGuideData.GuideStep) === null || _initialGuideData$Gui === void 0 ? void 0 : (_initialGuideData$Gui2 = _initialGuideData$Gui[0]) === null || _initialGuideData$Gui2 === void 0 ? void 0 : _initialGuideData$Gui2.TextFieldProperties.Text;\n  // const bannerSteps = initialGuideData.GuideStep.filter((step:any) => step.StepType === \"Banner\");\n\n  const renderHtmlSnippet = snippet => {\n    if (!snippet) return \"Sample Text...\"; // Return an empty string if snippet is null or undefined.\n    return snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\n      return `${p1}${p2}\" target=\"_blank\"${p3}`;\n    });\n  };\n  const image = imageSrc;\n  const isBase64 = url => url.startsWith(\"data:image/\");\n  const textField = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui3 = initialGuideData.GuideStep) === null || _initialGuideData$Gui3 === void 0 ? void 0 : (_initialGuideData$Gui4 = _initialGuideData$Gui3[0]) === null || _initialGuideData$Gui4 === void 0 ? void 0 : (_initialGuideData$Gui5 = _initialGuideData$Gui4.TextFieldProperties) === null || _initialGuideData$Gui5 === void 0 ? void 0 : _initialGuideData$Gui5[0]) || {};\n  const {\n    Text: textFieldText,\n    Alignment,\n    Hyperlink,\n    Emoji,\n    TextProperties\n  } = textField;\n  const {\n    Bold,\n    Italic,\n    TextColor\n  } = TextProperties || {};\n  const customButton = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui6 = initialGuideData.GuideStep) === null || _initialGuideData$Gui6 === void 0 ? void 0 : (_initialGuideData$Gui7 = _initialGuideData$Gui6[0]) === null || _initialGuideData$Gui7 === void 0 ? void 0 : (_initialGuideData$Gui8 = _initialGuideData$Gui7.ButtonSection) === null || _initialGuideData$Gui8 === void 0 ? void 0 : (_initialGuideData$Gui9 = _initialGuideData$Gui8.map(section => section.CustomButtons.map(button => ({\n    ...button,\n    ContainerId: section.Id // Attach the container ID for grouping\n  })))) === null || _initialGuideData$Gui9 === void 0 ? void 0 : _initialGuideData$Gui9.reduce((acc, curr) => acc.concat(curr), [])) || [];\n  const handleButtonClick = action => {\n    if (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\n      if (action.ActionValue === \"same-tab\") {\n        window.location.href = action.TargetUrl;\n      } else {\n        window.open(action.TargetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n      //onContinue();\n    } else if (action.Action === \"start-interaction\") {\n      // onContinue();\n      // setOverlayValue(false);\n    } else if (action.Action === \"close\") {\n      // onClose();\n      // setOverlayValue(false);\n    } else if (action.Action === \"Next\" || action.Action === \"next\") {\n      var _initialGuideData$Gui10;\n      // Handle next step navigation\n      if (typeof currentStep === 'number' && (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui10 = initialGuideData.GuideStep) === null || _initialGuideData$Gui10 === void 0 ? void 0 : _initialGuideData$Gui10.length) > currentStep) {\n        // Navigate to next step logic would go here\n      }\n    } else if (action.Action === \"Previous\" || action.Action === \"previous\") {\n      // Handle previous step navigation\n      if (typeof currentStep === 'number' && currentStep > 1) {\n        // Navigate to previous step logic would go here\n      }\n    } else if (action.Action === \"Restart\") {\n      var _initialGuideData$Gui11;\n      // Reset to the first step\n      // Navigate to first step logic would go here\n      if ((initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui11 = initialGuideData.GuideStep) === null || _initialGuideData$Gui11 === void 0 ? void 0 : _initialGuideData$Gui11.length) > 0) {\n        var _initialGuideData$Gui12;\n        // Reset to step 1\n        // If there's a specific URL for the first step, navigate to it\n        if ((_initialGuideData$Gui12 = initialGuideData.GuideStep[0]) !== null && _initialGuideData$Gui12 !== void 0 && _initialGuideData$Gui12.ElementPath) {\n          const firstStepElement = document.evaluate(initialGuideData.GuideStep[0].ElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    } else if (action === undefined || null) {\n      // onClose();\n      // setOverlayValue(false);\n    } else {\n      // onClose();\n      // setOverlayValue(false);\n    }\n  };\n  const designProps = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui13 = initialGuideData.GuideStep) === null || _initialGuideData$Gui13 === void 0 ? void 0 : (_initialGuideData$Gui14 = _initialGuideData$Gui13[0]) === null || _initialGuideData$Gui14 === void 0 ? void 0 : _initialGuideData$Gui14.Design) || {};\n  const IconColor = designProps.IconColor || \"#000\";\n  const IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\n  const canvas = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui15 = initialGuideData.GuideStep) === null || _initialGuideData$Gui15 === void 0 ? void 0 : (_initialGuideData$Gui16 = _initialGuideData$Gui15[0]) === null || _initialGuideData$Gui16 === void 0 ? void 0 : _initialGuideData$Gui16.Canvas) || {};\n  const BackgroundColor = (canvas === null || canvas === void 0 ? void 0 : canvas.BackgroundColor) || \"#f1f1f7\";\n  const BackgroundImage = canvas === null || canvas === void 0 ? void 0 : canvas.BackgroundImage;\n  const Width = canvas.Width || \"100%\";\n  const Radius = canvas.Radius || \"0\";\n  const Padding = canvas.Padding || \"10\";\n  const BorderSize = canvas.BorderSize || \"2\";\n  const BorderColor = canvas.BorderColor || \"#f1f1f7\";\n  const Position = \"absolute\";\n  const zindex = canvas.Zindex || \"999999\";\n  const Modal = initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui17 = initialGuideData.GuideStep) === null || _initialGuideData$Gui17 === void 0 ? void 0 : (_initialGuideData$Gui18 = _initialGuideData$Gui17[0]) === null || _initialGuideData$Gui18 === void 0 ? void 0 : _initialGuideData$Gui18.Modal;\n  const isCloseDisabled = (_Modal$DismissOption = Modal === null || Modal === void 0 ? void 0 : Modal.DismissOption) !== null && _Modal$DismissOption !== void 0 ? _Modal$DismissOption : false;\n  const Design = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui19 = initialGuideData.GuideStep) === null || _initialGuideData$Gui19 === void 0 ? void 0 : (_initialGuideData$Gui20 = _initialGuideData$Gui19[0]) === null || _initialGuideData$Gui20 === void 0 ? void 0 : _initialGuideData$Gui20.Design) || {};\n  const htmlSnippet = (initialGuideData === null || initialGuideData === void 0 ? void 0 : (_initialGuideData$Gui21 = initialGuideData.GuideStep) === null || _initialGuideData$Gui21 === void 0 ? void 0 : (_initialGuideData$Gui22 = _initialGuideData$Gui21[0]) === null || _initialGuideData$Gui22 === void 0 ? void 0 : _initialGuideData$Gui22.HtmlSnippet) || \"\";\n\n  // Apply overflow hidden to body when canvas position is \"Cover Top\"\n  useEffect(() => {\n    if ((canvas === null || canvas === void 0 ? void 0 : canvas.Position) === \"Cover Top\") {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [canvas === null || canvas === void 0 ? void 0 : canvas.Position]); // Re-run when canvas position changes\n\n  const countLinesFromHtml = html => {\n    const paragraphCount = (html.match(/<p>/g) || []).length;\n    const brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\n    return paragraphCount > 0 ? paragraphCount - 1 : 0;\n  };\n\n  // const renderHtmlSnippet = (snippet: string) => {\n  // \treturn parse(snippet, {\n  // \t\treplace: (domNode: any) => {\n  // \t\t\tif (domNode.name === \"font\") {\n  // \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\n  // \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\n  // \t\t\t\treturn (\n  // \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\n  // \t\t\t\t\t\t{domToReact(domNode.children)}\n  // \t\t\t\t\t</span>\n  // \t\t\t\t);\n  // \t\t\t}\n  // \t\t\treturn undefined;\n  // \t\t},\n  // \t});\n  // };\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      // position: \"relative\",\n      // top: \"55px\"\n    },\n    className: \"qadpt-container\",\n    children: showBanner && /*#__PURE__*/_jsxDEV(Box, {\n      className: \"qadpt-boxpre\",\n      id: \"guide-popup\",\n      sx: {\n        // position: \"relative\",\n        // top: \"55px\",\n        // ...BannerWrapper,\n        //top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\n        left: \"50%\",\n        height: \"auto\",\n        marginTop: `${16 + parseInt(Padding || 0) + parseInt(BorderSize || 0) + (customButton && customButton.length > 0 ? 4 : 0) + countLinesFromHtml(textFieldText) * 10}px`,\n        //\"29px\",\n        transform: \"translate(-50%, -50%)\",\n        backgroundColor: sectionColor,\n        //width: Width,\n        maxWidth: \"100%\",\n        //borderRadius: Radius,\n        padding: `${Padding}px`,\n        //borderWidth: \"2px\",\n        //border: `${BorderSize}px solid ${BorderColor}`,\n        boxShadow: Object.keys(canvas).length == 0 ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : (canvas === null || canvas === void 0 ? void 0 : canvas.Position) == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",\n        //(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\n        position: Position,\n        zIndex: zindex,\n        background: `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important`,\n        backgroundImage: BackgroundImage ? `url(${BackgroundImage})` : \"none\",\n        backgroundSize: \"cover\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        // border: `${BorderSize}px solid ${BorderColor}`,\n        borderTop: `${BorderSize}px solid ${BorderColor} !important`,\n        borderRight: `${BorderSize}px solid ${BorderColor} !important`,\n        borderLeft: `${BorderSize}px solid ${BorderColor} !important`,\n        borderBottom: `${BorderSize}px solid ${BorderColor} !important`\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-row\",\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            // margin: \"8px \",\n            //bottom: \"45px\",\n            position: \"relative\",\n            display: \"flex\",\n            alignItems: \"center\",\n            placeContent: \"center\",\n            width: \"100%\",\n            \"& .MuiTypography-root\": {\n              width: \"100%\",\n              margin: \"0\"\n            }\n          },\n          children: [Hyperlink ? /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"a\",\n            href: Hyperlink,\n            target: \"_blank\" // Open link in a new tab\n            ,\n            rel: \"noopener noreferrer\" // Security measure when using target=\"_blank\"\n            ,\n            sx: {\n              color: TextColor,\n              padding: \"5px 2px\",\n              textAlign: Alignment || \"center\",\n              marginTop: 1,\n              textDecoration: \"underline\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: renderHtmlSnippet(htmlSnippet)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 8\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"div\",\n            className: \"qadpt-preview qadpt-rte\",\n            sx: {\n              color: TextColor,\n              textAlign: Alignment,\n              marginTop: 1,\n              whiteSpace: \"pre-wrap\",\n              padding: \"5px 2px\",\n              wordBreak: \"break-word\",\n              \"& p\": {\n                margin: \"0\"\n              }\n            },\n            dangerouslySetInnerHTML: {\n              __html: renderHtmlSnippet(textFieldText)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 10\n          }, this), Emoji && /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"span\",\n            sx: {\n              fontWeight: Bold ? \"bold\" : \"normal\",\n              padding: \"5px 2px\",\n              fontStyle: Italic ? \"italic\" : \"normal\",\n              color: TextColor,\n              textAlign: Alignment ? Alignment : \"center\",\n              mt: 1\n            },\n            children: Emoji\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 8\n          }, this), customButton && customButton.some(button => button.ButtonName && button.ButtonName.trim() !== \"\") && /*#__PURE__*/_jsxDEV(DialogActions, {\n            sx: {\n              justifyContent: \"center\",\n              padding: \"0 !important\",\n              height: \"40px\"\n            },\n            children: customButton.map((button, index) => {\n              var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5;\n              return /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => handleButtonClick(button.ButtonAction),\n                variant: \"contained\",\n                style: {\n                  backgroundColor: (_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor,\n                  color: (_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor,\n                  border: (_button$ButtonPropert3 = button.ButtonProperties) !== null && _button$ButtonPropert3 !== void 0 && _button$ButtonPropert3.ButtonBorderColor ? `2px solid ${button.ButtonProperties.ButtonBorderColor}` : \"none\",\n                  margin: \"0 5px\",\n                  fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || 15,\n                  width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                  padding: \"6px 16px\",\n                  textTransform: \"none\",\n                  borderRadius: \"20px\",\n                  lineHeight: \"22px\"\n                },\n                sx: {\n                  \"&:hover\": {\n                    filter: \"brightness(1.2)\"\n                  }\n                },\n                children: button.ButtonName\n              }, button.Id || `button-${index}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 11\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 9\n          }, this), Hyperlink && /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"a\",\n            href: Hyperlink,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              color: TextColor,\n              textAlign: Alignment || \"left\",\n              mt: 1,\n              textDecoration: \"underline\"\n            },\n            children: renderHtmlSnippet(textFieldText)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 8\n          }, this), image && /*#__PURE__*/_jsxDEV(Box, {\n            component: \"a\",\n            href: ((_initialGuideData$Gui23 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui23 === void 0 ? void 0 : (_initialGuideData$Gui24 = _initialGuideData$Gui23.ImageProperties) === null || _initialGuideData$Gui24 === void 0 ? void 0 : _initialGuideData$Gui24.Hyperlink) || \"#\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              textAlign: Alignment || \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: isBase64(image) ? image : image,\n              sx: {\n                maxHeight: ((_initialGuideData$Gui25 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui25 === void 0 ? void 0 : (_initialGuideData$Gui26 = _initialGuideData$Gui25.ImageProperties) === null || _initialGuideData$Gui26 === void 0 ? void 0 : _initialGuideData$Gui26.MaxImageHeight) || \"auto\",\n                objectFit: ((_initialGuideData$Gui27 = initialGuideData.GuideStep[0]) === null || _initialGuideData$Gui27 === void 0 ? void 0 : (_initialGuideData$Gui28 = _initialGuideData$Gui27.ImageProperties) === null || _initialGuideData$Gui28 === void 0 ? void 0 : (_initialGuideData$Gui29 = _initialGuideData$Gui28.UploadedImages) === null || _initialGuideData$Gui29 === void 0 ? void 0 : (_initialGuideData$Gui30 = _initialGuideData$Gui29[0]) === null || _initialGuideData$Gui30 === void 0 ? void 0 : _initialGuideData$Gui30.Fit) || \"contain\",\n                display: \"block\",\n                margin: \"0 auto\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 6\n        }, this), \" \", isCloseDisabled && /*#__PURE__*/_jsxDEV(IconButton, {\n          sx: {\n            // position: \"fixed\",\n            boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\n            marginLeft: \"2px\",\n            background: \"#fff !important\",\n            border: \"1px solid #ccc\",\n            zIndex: \"999999\",\n            borderRadius: \"50px\",\n            padding: \"3px !important\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            sx: {\n              zoom: \"1\",\n              color: \"#000\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 6\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 3\n  }, this);\n};\n_s(BannerEndUser, \"UdbB1uYrRQ3fu7AoRdMnTo2F5tE=\", false, function () {\n  return [useDrawerStore, useDrawerStore];\n});\n_c = BannerEndUser;\nexport default BannerEndUser;\nvar _c;\n$RefreshReg$(_c, \"BannerEndUser\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Typography", "IconButton", "DialogActions", "CloseIcon", "useDrawerStore", "jsxDEV", "_jsxDEV", "BannerEndUser", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "backgroundC", "currentStep", "_s", "_initialGuideData$Gui", "_initialGuideData$Gui2", "_initialGuideData$Gui3", "_initialGuideData$Gui4", "_initialGuideData$Gui5", "_initialGuideData$Gui6", "_initialGuideData$Gui7", "_initialGuideData$Gui8", "_initialGuideData$Gui9", "_initialGuideData$Gui13", "_initialGuideData$Gui14", "_initialGuideData$Gui15", "_initialGuideData$Gui16", "_initialGuideData$Gui17", "_initialGuideData$Gui18", "_Modal$DismissOption", "_initialGuideData$Gui19", "_initialGuideData$Gui20", "_initialGuideData$Gui21", "_initialGuideData$Gui22", "_initialGuideData$Gui23", "_initialGuideData$Gui24", "_initialGuideData$Gui25", "_initialGuideData$Gui26", "_initialGuideData$Gui27", "_initialGuideData$Gui28", "_initialGuideData$Gui29", "_initialGuideData$Gui30", "btnBgColor", "btnTextColor", "btnBorderColor", "state", "showBanner", "setShowBanner", "setImageSrc", "imageSrc", "htmlContent", "sectionColor", "Teext", "GuideStep", "TextFieldProperties", "Text", "renderHtmlSnippet", "snippet", "replace", "match", "p1", "p2", "p3", "image", "isBase64", "url", "startsWith", "textField", "textFieldText", "Alignment", "Hyperlink", "<PERSON><PERSON><PERSON>", "TextProperties", "Bold", "Italic", "TextColor", "customButton", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "handleButtonClick", "action", "Action", "ActionValue", "window", "location", "href", "TargetUrl", "open", "_initialGuideData$Gui10", "length", "_initialGuideData$Gui11", "_initialGuideData$Gui12", "<PERSON>ement<PERSON><PERSON>", "firstStepElement", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "scrollIntoView", "behavior", "undefined", "designProps", "Design", "IconColor", "IconOpacity", "QuietIcon", "canvas", "<PERSON><PERSON>", "BackgroundColor", "BackgroundImage", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Padding", "BorderSize", "BorderColor", "Position", "zindex", "Zindex", "Modal", "isCloseDisabled", "DismissOption", "htmlSnippet", "HtmlSnippet", "body", "style", "overflow", "countLinesFromHtml", "html", "paragraphCount", "brCount", "sx", "className", "children", "id", "left", "height", "marginTop", "parseInt", "transform", "backgroundColor", "max<PERSON><PERSON><PERSON>", "padding", "boxShadow", "Object", "keys", "position", "zIndex", "background", "backgroundImage", "backgroundSize", "backgroundPosition", "backgroundRepeat", "borderTop", "borderRight", "borderLeft", "borderBottom", "display", "alignItems", "place<PERSON><PERSON>nt", "width", "margin", "component", "target", "rel", "color", "textAlign", "textDecoration", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "whiteSpace", "wordBreak", "fontWeight", "fontStyle", "mt", "some", "ButtonName", "trim", "justifyContent", "index", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "onClick", "ButtonAction", "variant", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "border", "ButtonBorderColor", "fontSize", "FontSize", "textTransform", "borderRadius", "lineHeight", "filter", "ImageProperties", "src", "maxHeight", "MaxImageHeight", "objectFit", "UploadedImages", "Fit", "marginLeft", "zoom", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Bannerspreview/Banner.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Box, Button, Typography, IconButton, DialogActions } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { CustomIconButton } from \"./Button\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport parse, { domToReact } from \"html-react-parser\";\r\nimport { Element } from \"domhandler\";\r\nimport { IconButtonSX } from \"./Banner.style\";\r\n\r\nconst BannerEndUser = ({ showBannerenduser, setShowBannerenduser, initialGuideData, backgroundC,currentStep }: any) => {\r\n\tconst {\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [showBanner, setShowBanner] = useState(true);\r\n\tconst { setImageSrc, imageSrc, htmlContent, sectionColor } = useDrawerStore((state: any) => state);\r\n\tconst Teext = initialGuideData?.GuideStep?.[0]?.TextFieldProperties.Text;\r\n\t// const bannerSteps = initialGuideData.GuideStep.filter((step:any) => step.StepType === \"Banner\");\r\n\r\n\tconst renderHtmlSnippet = (snippet: string | undefined | null) => {\r\n\t\tif (!snippet) return \"Sample Text...\"; // Return an empty string if snippet is null or undefined.\r\n\t\treturn snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (match, p1, p2, p3) => {\r\n\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t});\r\n\t};\r\n\tconst image = imageSrc;\r\n\tconst isBase64 = (url: string) => url.startsWith(\"data:image/\");\r\n\tconst textField = initialGuideData?.GuideStep?.[0]?.TextFieldProperties?.[0] || {};\r\n\tconst { Text: textFieldText, Alignment, Hyperlink, Emoji, TextProperties } = textField;\r\n\tconst { Bold, Italic, TextColor } = TextProperties || {};\r\n\tconst customButton =\r\n    initialGuideData?.GuideStep?.[0]?.ButtonSection\r\n        ?.map((section: any) =>\r\n            section.CustomButtons.map((button: any) => ({\r\n                ...button,\r\n                ContainerId: section.Id, // Attach the container ID for grouping\r\n            }))\r\n        )\r\n        ?.reduce((acc: any[], curr: any[]) => acc.concat(curr), []) || [];\r\n\r\n\tconst handleButtonClick = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"openurl\" || action.Action === \"open\") {\r\n\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\twindow.location.href = action.TargetUrl;\r\n\t\t} else {\r\n\t\twindow.open(action.TargetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t}\r\n\t\t//onContinue();\r\n\t\t} else if (action.Action === \"start-interaction\") {\r\n\t\t\t// onContinue();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"close\") {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else if (action.Action === \"Next\" || action.Action === \"next\") {\r\n\t\t\t// Handle next step navigation\r\n\t\t\tif (typeof currentStep === 'number' && initialGuideData?.GuideStep?.length > currentStep) {\r\n\t\t\t\t// Navigate to next step logic would go here\r\n\t\t\t}\r\n\t\t} else if (action.Action === \"Previous\" || action.Action === \"previous\") {\r\n\t\t\t// Handle previous step navigation\r\n\t\t\tif (typeof currentStep === 'number' && currentStep > 1) {\r\n\t\t\t\t// Navigate to previous step logic would go here\r\n\t\t\t}\r\n\t\t} else if (action.Action === \"Restart\") {\r\n\t\t\t// Reset to the first step\r\n\t\t\t// Navigate to first step logic would go here\r\n\t\t\tif (initialGuideData?.GuideStep?.length > 0) {\r\n\t\t\t\t// Reset to step 1\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (initialGuideData.GuideStep[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = document.evaluate(\r\n\t\t\t\t\t\tinitialGuideData.GuideStep[0].ElementPath,\r\n\t\t\t\t\t\tdocument,\r\n\t\t\t\t\t\tnull,\r\n\t\t\t\t\t\tXPathResult.FIRST_ORDERED_NODE_TYPE,\r\n\t\t\t\t\t\tnull\r\n\t\t\t\t\t).singleNodeValue;\r\n\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\t(firstStepElement as HTMLElement).scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} else if (action === undefined || null) {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t} else {\r\n\t\t\t// onClose();\r\n\t\t\t// setOverlayValue(false);\r\n\t\t}\r\n\t};\r\n\tconst designProps = initialGuideData?.GuideStep?.[0]?.Design || {};\r\n\tconst IconColor = designProps.IconColor || \"#000\";\r\n\tconst IconOpacity = designProps.QuietIcon ? 0.5 : 1.0;\r\n\tconst canvas = initialGuideData?.GuideStep?.[0]?.Canvas || {};\r\n\tconst BackgroundColor = canvas?.BackgroundColor\t|| \"#f1f1f7\";\r\n\tconst BackgroundImage = canvas?.BackgroundImage;\r\n\tconst Width = canvas.Width || \"100%\";\r\n\tconst Radius = canvas.Radius || \"0\";\r\n\tconst Padding = canvas.Padding || \"10\";\r\n\tconst BorderSize = canvas.BorderSize || \"2\";\r\n\tconst BorderColor = canvas.BorderColor || \"#f1f1f7\";\r\n\tconst Position =  \"absolute\";\r\n\tconst zindex = canvas.Zindex || \"999999\";\r\n\tconst Modal = initialGuideData?.GuideStep?.[0]?.Modal;\r\n\tconst isCloseDisabled = Modal?.DismissOption ?? false;\r\n\tconst Design = initialGuideData?.GuideStep?.[0]?.Design || {};\r\n\tconst htmlSnippet = initialGuideData?.GuideStep?.[0]?.HtmlSnippet || \"\";\r\n\r\n\t// Apply overflow hidden to body when canvas position is \"Cover Top\"\r\n\tuseEffect(() => {\r\n\t\tif (canvas?.Position === \"Cover Top\") {\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t} else {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t}\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tdocument.body.style.overflow = \"\";\r\n\t\t};\r\n\t}, [canvas?.Position]); // Re-run when canvas position changes\r\n\r\n\tconst countLinesFromHtml = (html: string) => {\r\n\t\tconst paragraphCount = (html.match(/<p>/g) || []).length;\r\n\r\n\t\tconst brCount = (html.match(/<br\\s*\\/?>/g) || []).length;\r\n\r\n\t\treturn paragraphCount>0 ? paragraphCount-1 : 0;\r\n\t};\r\n\r\n\t// const renderHtmlSnippet = (snippet: string) => {\r\n\t// \treturn parse(snippet, {\r\n\t// \t\treplace: (domNode: any) => {\r\n\t// \t\t\tif (domNode.name === \"font\") {\r\n\t// \t\t\t\tconst { size, color, ...otherAttributes } = domNode.attribs || {};\r\n\t// \t\t\t\tconst fontSize = size ? `${parseInt(size, 10) * 5}px` : \"inherit\";\r\n\t// \t\t\t\treturn (\r\n\t// \t\t\t\t\t<span style={{ fontSize, color: color || \"inherit\", ...otherAttributes }}>\r\n\t// \t\t\t\t\t\t{domToReact(domNode.children)}\r\n\t// \t\t\t\t\t</span>\r\n\t// \t\t\t\t);\r\n\t// \t\t\t}\r\n\t// \t\t\treturn undefined;\r\n\t// \t\t},\r\n\t// \t});\r\n\t// };\r\n\r\n\treturn (\r\n\t\t<Box sx={{\r\n\t\t\t// position: \"relative\",\r\n\t\t\t// top: \"55px\"\r\n\t\t}} className=\"qadpt-container\">\r\n\t\t\t{showBanner && (\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-boxpre\"\r\n\t\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t// position: \"relative\",\r\n\t\t\t\t\t\t// top: \"55px\",\r\n\t\t\t\t\t\t// ...BannerWrapper,\r\n\t\t\t\t\t\t//top: \"55px\",//customButton && customButton.length > 0 ? \"87px\" : \"82px\",\r\n\t\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmarginTop: `${16+parseInt(Padding||0)+parseInt(BorderSize||0)+(customButton && customButton.length > 0 ? 4 : 0)+(countLinesFromHtml(textFieldText)*10)}px`,//\"29px\",\r\n\t\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\t\tbackgroundColor: sectionColor,\r\n\t\t\t\t\t\t//width: Width,\r\n\t\t\t\t\t\tmaxWidth:\"100%\",\r\n\t\t\t\t\t\t//borderRadius: Radius,\r\n\t\t\t\t\t\tpadding: `${Padding}px`,\r\n\t\t\t\t\t\t//borderWidth: \"2px\",\r\n\t\t\t\t\t\t//border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tboxShadow: Object.keys(canvas).length == 0? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : canvas?.Position == \"Cover Top\" ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",//(canvas.toString.length == 0 || canvas?.Position === \"Cover Top\") ? \"0px 1px 15px rgba(0, 0, 0, 0.7)\" : \"none\",// Here, checking if the Canvas is undefined then we will apply shadow means it is defaulty Cover Top and if not undefined we will asign based on position\r\n\t\t\t\t\t\tposition: Position,\r\n\t\t\t\t\t\tzIndex: zindex,\r\n\r\n\t\t\t\t\t\tbackground:  `${BackgroundColor ? BackgroundColor : '#f1f1f7'} !important` ,\r\n\t\t\t\t\t\tbackgroundImage: BackgroundImage ? `url(${BackgroundImage})` : \"none\",\r\n\t\t\t\t\t\tbackgroundSize: \"cover\",\r\n\t\t\t\t\t\tbackgroundPosition: \"center\",\r\n\t\t\t\t\t\tbackgroundRepeat: \"no-repeat\",\r\n\t\t\t\t\t\t// border: `${BorderSize}px solid ${BorderColor}`,\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t\t `${BorderSize}px solid ${BorderColor} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t  `${BorderSize}px solid ${BorderColor} !important`,\r\n\r\n\t\t\t\t\tborderBottom: `${BorderSize}px solid ${BorderColor} !important`,\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Box className=\"qadpt-row\" sx={{  display: \"flex\", alignItems: \"center\"}}>\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// margin: \"8px \",\r\n\t\t\t\t\t\t\t//bottom: \"45px\",\r\n\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\r\n\r\n\t\t\t\t\t\t\t\"& .MuiTypography-root\": {\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\tmargin: \"0\",\r\n\t\t\t\t\t\t\t},\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Display hyperlink  */}\r\n\t\t\t\t\t\t{Hyperlink ? (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\" // Open link in a new tab\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\" // Security measure when using target=\"_blank\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: renderHtmlSnippet(htmlSnippet) }}\r\n\t\t\t\t\t\t\t></Typography>\r\n\t\t\t\t\t\t\t) : (\r\n\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tcomponent = \"div\"\r\n\t\t\t\t\t\t\t\t  className=\"qadpt-preview qadpt-rte\"\r\n\t\t\t\t\t\t\t\t  sx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment,\r\n\t\t\t\t\t\t\t\t\tmarginTop: 1,\r\n\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\"& p\": {\r\n\t\t\t\t\t\t\t\t\t  margin: \"0\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t  dangerouslySetInnerHTML={{ __html: renderHtmlSnippet(textFieldText) }}\r\n\t\t\t\t\t\t\t\t/>\r\n\r\n\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{Emoji && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontWeight: Bold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"5px 2px\",\r\n\t\t\t\t\t\t\t\t\tfontStyle: Italic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment ? Alignment : \"center\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{Emoji}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t{customButton &&\r\n\t\t\t\t\t\t\tcustomButton.some((button: any) => button.ButtonName && button.ButtonName.trim() !== \"\") && (\r\n\t\t\t\t\t\t\t\t<DialogActions\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px\"\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{customButton.map((button: any, index: any) => (\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tkey={button.Id || `button-${index}`}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${button.ButtonProperties.ButtonBorderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || 15,\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"22px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfilter: \"brightness(1.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</DialogActions>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{/* {CustomButton && CustomButton.ButtonName && (\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tfontSize: CustomButton.ButtonProperties?.FontSize || 14,\r\n\t\t\t\t\t\t\t\t\twidth: CustomButton.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\tpadding: CustomButton.ButtonProperties?.Padding || \"10px\",\r\n\t\t\t\t\t\t\t\t\tcolor: CustomButton.ButtonProperties?.ButtonTextColor,\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: CustomButton.ButtonProperties?.ButtonBackgroundColor,\r\n\t\t\t\t\t\t\t\t\t//textAlign: CustomButton.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t//display: \"block\",\r\n\t\t\t\t\t\t\t\t\tmargin: \"8px\",\r\n\t\t\t\t\t\t\t\t\tlineHeight: \"0px\",\r\n\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\tmx: CustomButton.Alignment === \"center\" ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonClick={handleButtonClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{CustomButton.ButtonName}\r\n\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t)} */}\r\n\r\n\t\t\t\t\t\t{Hyperlink && (\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={Hyperlink}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tcolor: TextColor,\r\n\t\t\t\t\t\t\t\t\ttextAlign: Alignment || \"left\",\r\n\t\t\t\t\t\t\t\t\tmt: 1,\r\n\t\t\t\t\t\t\t\t\ttextDecoration: \"underline\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{renderHtmlSnippet(textFieldText)}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t{image && (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tcomponent=\"a\"\r\n\t\t\t\t\t\t\t\thref={initialGuideData.GuideStep[0]?.ImageProperties?.Hyperlink || \"#\"}\r\n\t\t\t\t\t\t\t\ttarget=\"_blank\"\r\n\t\t\t\t\t\t\t\trel=\"noopener noreferrer\"\r\n\t\t\t\t\t\t\t\tsx={{ textAlign: Alignment || \"center\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\tsrc={isBase64(image) ? image : image}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tmaxHeight: initialGuideData.GuideStep[0]?.ImageProperties?.MaxImageHeight || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\tobjectFit: initialGuideData.GuideStep[0]?.ImageProperties?.UploadedImages?.[0]?.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"0 auto\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>{\" \"}\r\n\t\t\t\t\t{isCloseDisabled && (\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n\t\t\t\t\tmarginLeft: \"2px\",\r\n\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\tzIndex:\"999999\",\r\n\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\tpadding:\"3px !important\"\r\n\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\"}}   />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</Box>\r\n\t);\r\n};\r\n\r\nexport default BannerEndUser;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AAClF,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMrD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,iBAAiB;EAAEC,oBAAoB;EAAEC,gBAAgB;EAAEC,WAAW;EAACC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,oBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACtH,MAAM;IACLC,UAAU;IACVC,YAAY;IACZC;EACD,CAAC,GAAGxC,cAAc,CAAEyC,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM;IAAEoD,WAAW;IAAEC,QAAQ;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAG/C,cAAc,CAAEyC,KAAU,IAAKA,KAAK,CAAC;EAClG,MAAMO,KAAK,GAAG1C,gBAAgB,aAAhBA,gBAAgB,wBAAAI,qBAAA,GAAhBJ,gBAAgB,CAAE2C,SAAS,cAAAvC,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,uBAAhCA,sBAAA,CAAkCuC,mBAAmB,CAACC,IAAI;EACxE;;EAEA,MAAMC,iBAAiB,GAAIC,OAAkC,IAAK;IACjE,IAAI,CAACA,OAAO,EAAE,OAAO,gBAAgB,CAAC,CAAC;IACvC,OAAOA,OAAO,CAACC,OAAO,CAAC,qCAAqC,EAAE,CAACC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;MACpF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;IAC1C,CAAC,CAAC;EACH,CAAC;EACD,MAAMC,KAAK,GAAGd,QAAQ;EACtB,MAAMe,QAAQ,GAAIC,GAAW,IAAKA,GAAG,CAACC,UAAU,CAAC,aAAa,CAAC;EAC/D,MAAMC,SAAS,GAAG,CAAAzD,gBAAgB,aAAhBA,gBAAgB,wBAAAM,sBAAA,GAAhBN,gBAAgB,CAAE2C,SAAS,cAAArC,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCqC,mBAAmB,cAAApC,sBAAA,uBAArDA,sBAAA,CAAwD,CAAC,CAAC,KAAI,CAAC,CAAC;EAClF,MAAM;IAAEqC,IAAI,EAAEa,aAAa;IAAEC,SAAS;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAe,CAAC,GAAGL,SAAS;EACtF,MAAM;IAAEM,IAAI;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGH,cAAc,IAAI,CAAC,CAAC;EACxD,MAAMI,YAAY,GACf,CAAAlE,gBAAgB,aAAhBA,gBAAgB,wBAAAS,sBAAA,GAAhBT,gBAAgB,CAAE2C,SAAS,cAAAlC,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCyD,aAAa,cAAAxD,sBAAA,wBAAAC,sBAAA,GAA/CD,sBAAA,CACMyD,GAAG,CAAEC,OAAY,IACfA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;IACxC,GAAGA,MAAM;IACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;EAC7B,CAAC,CAAC,CACN,CAAC,cAAA7D,sBAAA,uBANLA,sBAAA,CAOM8D,MAAM,CAAC,CAACC,GAAU,EAAEC,IAAW,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EAAE;EAExE,MAAME,iBAAiB,GAAIC,MAAW,IAAK;IAC1C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,EAAE;MAC7F,IAAID,MAAM,CAACE,WAAW,KAAK,UAAU,EAAE;QACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGL,MAAM,CAACM,SAAS;MACvC,CAAC,MAAM;QACPH,MAAM,CAACI,IAAI,CAACP,MAAM,CAACM,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MAC9D;MACA;IACA,CAAC,MAAM,IAAIN,MAAM,CAACC,MAAM,KAAK,mBAAmB,EAAE;MACjD;MACA;IAAA,CACA,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,OAAO,EAAE;MACrC;MACA;IAAA,CACA,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,EAAE;MAAA,IAAAO,uBAAA;MAChE;MACA,IAAI,OAAOrF,WAAW,KAAK,QAAQ,IAAI,CAAAF,gBAAgB,aAAhBA,gBAAgB,wBAAAuF,uBAAA,GAAhBvF,gBAAgB,CAAE2C,SAAS,cAAA4C,uBAAA,uBAA3BA,uBAAA,CAA6BC,MAAM,IAAGtF,WAAW,EAAE;QACzF;MAAA;IAEF,CAAC,MAAM,IAAI6E,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,UAAU,EAAE;MACxE;MACA,IAAI,OAAO9E,WAAW,KAAK,QAAQ,IAAIA,WAAW,GAAG,CAAC,EAAE;QACvD;MAAA;IAEF,CAAC,MAAM,IAAI6E,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAAA,IAAAS,uBAAA;MACvC;MACA;MACA,IAAI,CAAAzF,gBAAgB,aAAhBA,gBAAgB,wBAAAyF,uBAAA,GAAhBzF,gBAAgB,CAAE2C,SAAS,cAAA8C,uBAAA,uBAA3BA,uBAAA,CAA6BD,MAAM,IAAG,CAAC,EAAE;QAAA,IAAAE,uBAAA;QAC5C;QACA;QACA,KAAAA,uBAAA,GAAI1F,gBAAgB,CAAC2C,SAAS,CAAC,CAAC,CAAC,cAAA+C,uBAAA,eAA7BA,uBAAA,CAA+BC,WAAW,EAAE;UAC/C,MAAMC,gBAAgB,GAAGC,QAAQ,CAACC,QAAQ,CACzC9F,gBAAgB,CAAC2C,SAAS,CAAC,CAAC,CAAC,CAACgD,WAAW,EACzCE,QAAQ,EACR,IAAI,EACJE,WAAW,CAACC,uBAAuB,EACnC,IACD,CAAC,CAACC,eAAe;UAEjB,IAAIL,gBAAgB,EAAE;YACpBA,gBAAgB,CAAiBM,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACzE;QACD;MACD;IACD,CAAC,MAAM,IAAIpB,MAAM,KAAKqB,SAAS,IAAI,IAAI,EAAE;MACxC;MACA;IAAA,CACA,MAAM;MACN;MACA;IAAA;EAEF,CAAC;EACD,MAAMC,WAAW,GAAG,CAAArG,gBAAgB,aAAhBA,gBAAgB,wBAAAa,uBAAA,GAAhBb,gBAAgB,CAAE2C,SAAS,cAAA9B,uBAAA,wBAAAC,uBAAA,GAA3BD,uBAAA,CAA8B,CAAC,CAAC,cAAAC,uBAAA,uBAAhCA,uBAAA,CAAkCwF,MAAM,KAAI,CAAC,CAAC;EAClE,MAAMC,SAAS,GAAGF,WAAW,CAACE,SAAS,IAAI,MAAM;EACjD,MAAMC,WAAW,GAAGH,WAAW,CAACI,SAAS,GAAG,GAAG,GAAG,GAAG;EACrD,MAAMC,MAAM,GAAG,CAAA1G,gBAAgB,aAAhBA,gBAAgB,wBAAAe,uBAAA,GAAhBf,gBAAgB,CAAE2C,SAAS,cAAA5B,uBAAA,wBAAAC,uBAAA,GAA3BD,uBAAA,CAA8B,CAAC,CAAC,cAAAC,uBAAA,uBAAhCA,uBAAA,CAAkC2F,MAAM,KAAI,CAAC,CAAC;EAC7D,MAAMC,eAAe,GAAG,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,eAAe,KAAI,SAAS;EAC5D,MAAMC,eAAe,GAAGH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,eAAe;EAC/C,MAAMC,KAAK,GAAGJ,MAAM,CAACI,KAAK,IAAI,MAAM;EACpC,MAAMC,MAAM,GAAGL,MAAM,CAACK,MAAM,IAAI,GAAG;EACnC,MAAMC,OAAO,GAAGN,MAAM,CAACM,OAAO,IAAI,IAAI;EACtC,MAAMC,UAAU,GAAGP,MAAM,CAACO,UAAU,IAAI,GAAG;EAC3C,MAAMC,WAAW,GAAGR,MAAM,CAACQ,WAAW,IAAI,SAAS;EACnD,MAAMC,QAAQ,GAAI,UAAU;EAC5B,MAAMC,MAAM,GAAGV,MAAM,CAACW,MAAM,IAAI,QAAQ;EACxC,MAAMC,KAAK,GAAGtH,gBAAgB,aAAhBA,gBAAgB,wBAAAiB,uBAAA,GAAhBjB,gBAAgB,CAAE2C,SAAS,cAAA1B,uBAAA,wBAAAC,uBAAA,GAA3BD,uBAAA,CAA8B,CAAC,CAAC,cAAAC,uBAAA,uBAAhCA,uBAAA,CAAkCoG,KAAK;EACrD,MAAMC,eAAe,IAAApG,oBAAA,GAAGmG,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,aAAa,cAAArG,oBAAA,cAAAA,oBAAA,GAAI,KAAK;EACrD,MAAMmF,MAAM,GAAG,CAAAtG,gBAAgB,aAAhBA,gBAAgB,wBAAAoB,uBAAA,GAAhBpB,gBAAgB,CAAE2C,SAAS,cAAAvB,uBAAA,wBAAAC,uBAAA,GAA3BD,uBAAA,CAA8B,CAAC,CAAC,cAAAC,uBAAA,uBAAhCA,uBAAA,CAAkCiF,MAAM,KAAI,CAAC,CAAC;EAC7D,MAAMmB,WAAW,GAAG,CAAAzH,gBAAgB,aAAhBA,gBAAgB,wBAAAsB,uBAAA,GAAhBtB,gBAAgB,CAAE2C,SAAS,cAAArB,uBAAA,wBAAAC,uBAAA,GAA3BD,uBAAA,CAA8B,CAAC,CAAC,cAAAC,uBAAA,uBAAhCA,uBAAA,CAAkCmG,WAAW,KAAI,EAAE;;EAEvE;EACAvI,SAAS,CAAC,MAAM;IACf,IAAI,CAAAuH,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,QAAQ,MAAK,WAAW,EAAE;MACrCtB,QAAQ,CAAC8B,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACxC,CAAC,MAAM;MACNhC,QAAQ,CAAC8B,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC;;IAEA;IACA,OAAO,MAAM;MACZhC,QAAQ,CAAC8B,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IAClC,CAAC;EACF,CAAC,EAAE,CAACnB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExB,MAAMW,kBAAkB,GAAIC,IAAY,IAAK;IAC5C,MAAMC,cAAc,GAAG,CAACD,IAAI,CAAC9E,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAEuC,MAAM;IAExD,MAAMyC,OAAO,GAAG,CAACF,IAAI,CAAC9E,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAEuC,MAAM;IAExD,OAAOwC,cAAc,GAAC,CAAC,GAAGA,cAAc,GAAC,CAAC,GAAG,CAAC;EAC/C,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBACCpI,OAAA,CAACR,GAAG;IAAC8I,EAAE,EAAE;MACR;MACA;IAAA,CACC;IAACC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,EAC5BhG,UAAU,iBACVxC,OAAA,CAACR,GAAG;MACH+I,SAAS,EAAC,cAAc;MACxBE,EAAE,EAAC,aAAa;MAChBH,EAAE,EAAE;QACH;QACA;QACA;QACA;QACAI,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,GAAG,EAAE,GAACC,QAAQ,CAACzB,OAAO,IAAE,CAAC,CAAC,GAACyB,QAAQ,CAACxB,UAAU,IAAE,CAAC,CAAC,IAAE/C,YAAY,IAAIA,YAAY,CAACsB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAEsC,kBAAkB,CAACpE,aAAa,CAAC,GAAC,EAAG,IAAI;QAAC;QAC3JgF,SAAS,EAAE,uBAAuB;QAClCC,eAAe,EAAElG,YAAY;QAC7B;QACAmG,QAAQ,EAAC,MAAM;QACf;QACAC,OAAO,EAAE,GAAG7B,OAAO,IAAI;QACvB;QACA;QACA8B,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACtC,MAAM,CAAC,CAAClB,MAAM,IAAI,CAAC,GAAE,iCAAiC,GAAG,CAAAkB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,QAAQ,KAAI,WAAW,GAAG,iCAAiC,GAAG,MAAM;QAAC;QAC7J8B,QAAQ,EAAE9B,QAAQ;QAClB+B,MAAM,EAAE9B,MAAM;QAEd+B,UAAU,EAAG,GAAGvC,eAAe,GAAGA,eAAe,GAAG,SAAS,aAAa;QAC1EwC,eAAe,EAAEvC,eAAe,GAAG,OAAOA,eAAe,GAAG,GAAG,MAAM;QACrEwC,cAAc,EAAE,OAAO;QACvBC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE,WAAW;QAC7B;QACAC,SAAS,EACP,GAAGvC,UAAU,YAAYC,WAAW,aAAa;QAEjDuC,WAAW,EACV,GAAGxC,UAAU,YAAYC,WAAW,aAAa;QAElDwC,UAAU,EACV,GAAGzC,UAAU,YAAYC,WAAW,aAAa;QAEpDyC,YAAY,EAAE,GAAG1C,UAAU,YAAYC,WAAW;MAClD,CAAE;MAAAkB,QAAA,eAEFxI,OAAA,CAACR,GAAG;QAAC+I,SAAS,EAAC,WAAW;QAACD,EAAE,EAAE;UAAG0B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAQ,CAAE;QAAAzB,QAAA,gBACzExI,OAAA,CAACR,GAAG;UACH8I,EAAE,EAAE;YACH;YACA;YACAe,QAAQ,EAAE,UAAU;YACpBW,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,YAAY,EAAE,QAAQ;YACrBC,KAAK,EAAE,MAAM;YAGd,uBAAuB,EAAE;cACxBA,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE;YACT;UAED,CAAE;UAAA5B,QAAA,GAGDxE,SAAS,gBACThE,OAAA,CAACN,UAAU;YACV2K,SAAS,EAAC,GAAG;YACb7E,IAAI,EAAExB,SAAU;YAChBsG,MAAM,EAAC,QAAQ,CAAC;YAAA;YAChBC,GAAG,EAAC,qBAAqB,CAAC;YAAA;YAC1BjC,EAAE,EAAE;cACHkC,KAAK,EAAEnG,SAAS;cAChB4E,OAAO,EAAE,SAAS;cAClBwB,SAAS,EAAE1G,SAAS,IAAI,QAAQ;cAChC6E,SAAS,EAAE,CAAC;cACZ8B,cAAc,EAAE;YACjB,CAAE;YACFC,uBAAuB,EAAE;cAAEC,MAAM,EAAE1H,iBAAiB,CAAC2E,WAAW;YAAE;UAAE;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,gBAGZhL,OAAA,CAACN,UAAU;YACV2K,SAAS,EAAG,KAAK;YACjB9B,SAAS,EAAC,yBAAyB;YACnCD,EAAE,EAAE;cACLkC,KAAK,EAAEnG,SAAS;cAChBoG,SAAS,EAAE1G,SAAS;cACpB6E,SAAS,EAAE,CAAC;cACZqC,UAAU,EAAE,UAAU;cACtBhC,OAAO,EAAE,SAAS;cAClBiC,SAAS,EAAE,YAAY;cACvB,KAAK,EAAE;gBACLd,MAAM,EAAE;cACV;YACC,CAAE;YACFO,uBAAuB,EAAE;cAAEC,MAAM,EAAE1H,iBAAiB,CAACY,aAAa;YAAE;UAAE;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAGF,EACA/G,KAAK,iBACLjE,OAAA,CAACN,UAAU;YACV2K,SAAS,EAAC,MAAM;YAChB/B,EAAE,EAAE;cACH6C,UAAU,EAAEhH,IAAI,GAAG,MAAM,GAAG,QAAQ;cACpC8E,OAAO,EAAE,SAAS;cAClBmC,SAAS,EAAEhH,MAAM,GAAG,QAAQ,GAAG,QAAQ;cACvCoG,KAAK,EAAEnG,SAAS;cAChBoG,SAAS,EAAE1G,SAAS,GAAGA,SAAS,GAAG,QAAQ;cAC3CsH,EAAE,EAAE;YACL,CAAE;YAAA7C,QAAA,EAEDvE;UAAK;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACZ,EACA1G,YAAY,IACZA,YAAY,CAACgH,IAAI,CAAE3G,MAAW,IAAKA,MAAM,CAAC4G,UAAU,IAAI5G,MAAM,CAAC4G,UAAU,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,iBACvFxL,OAAA,CAACJ,aAAa;YACb0I,EAAE,EAAE;cACJmD,cAAc,EAAE,QAAQ;cACvBxC,OAAO,EAAE,cAAc;cACxBN,MAAM,EAAE;YACR,CAAE;YAAAH,QAAA,EAEDlE,YAAY,CAACE,GAAG,CAAC,CAACG,MAAW,EAAE+G,KAAU;cAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACzC/L,OAAA,CAACP,MAAM;gBAENuM,OAAO,EAAEA,CAAA,KAAM9G,iBAAiB,CAACP,MAAM,CAACsH,YAAY,CAAE;gBACtDC,OAAO,EAAC,WAAW;gBACnBlE,KAAK,EAAE;kBACNe,eAAe,GAAA4C,qBAAA,GAAEhH,MAAM,CAACwH,gBAAgB,cAAAR,qBAAA,uBAAvBA,qBAAA,CAAyBS,qBAAqB;kBAC/D5B,KAAK,GAAAoB,sBAAA,GAAEjH,MAAM,CAACwH,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBS,eAAe;kBAC/CC,MAAM,EAAE,CAAAT,sBAAA,GAAAlH,MAAM,CAACwH,gBAAgB,cAAAN,sBAAA,eAAvBA,sBAAA,CAAyBU,iBAAiB,GAC/C,aAAa5H,MAAM,CAACwH,gBAAgB,CAACI,iBAAiB,EAAE,GACxD,MAAM;kBACTnC,MAAM,EAAE,OAAO;kBACfoC,QAAQ,EAAE,EAAAV,sBAAA,GAAAnH,MAAM,CAACwH,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyBW,QAAQ,KAAI,EAAE;kBACjDtC,KAAK,EAAE,EAAA4B,sBAAA,GAAApH,MAAM,CAACwH,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyB7E,KAAK,KAAI,MAAM;kBAC/C+B,OAAO,EAAE,UAAU;kBACnByD,aAAa,EAAE,MAAM;kBACrBC,YAAY,EAAE,MAAM;kBACpBC,UAAU,EAAE;gBACb,CAAE;gBACFtE,EAAE,EAAE;kBACH,SAAS,EAAE;oBACVuE,MAAM,EAAE;kBACT;gBACD,CAAE;gBAAArE,QAAA,EAED7D,MAAM,CAAC4G;cAAU,GAvBb5G,MAAM,CAACE,EAAE,IAAI,UAAU6G,KAAK,EAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwB5B,CAAC;YAAA,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACY,CACf,EAwBDhH,SAAS,iBACThE,OAAA,CAACN,UAAU;YACV2K,SAAS,EAAC,GAAG;YACb7E,IAAI,EAAExB,SAAU;YAChBsG,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBjC,EAAE,EAAE;cACHkC,KAAK,EAAEnG,SAAS;cAChBoG,SAAS,EAAE1G,SAAS,IAAI,MAAM;cAC9BsH,EAAE,EAAE,CAAC;cACLX,cAAc,EAAE;YACjB,CAAE;YAAAlC,QAAA,EAEDtF,iBAAiB,CAACY,aAAa;UAAC;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CACZ,EAEAvH,KAAK,iBACLzD,OAAA,CAACR,GAAG;YACH6K,SAAS,EAAC,GAAG;YACb7E,IAAI,EAAE,EAAA5D,uBAAA,GAAAxB,gBAAgB,CAAC2C,SAAS,CAAC,CAAC,CAAC,cAAAnB,uBAAA,wBAAAC,uBAAA,GAA7BD,uBAAA,CAA+BkL,eAAe,cAAAjL,uBAAA,uBAA9CA,uBAAA,CAAgDmC,SAAS,KAAI,GAAI;YACvEsG,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBjC,EAAE,EAAE;cAAEmC,SAAS,EAAE1G,SAAS,IAAI;YAAS,CAAE;YAAAyE,QAAA,eAEzCxI,OAAA,CAACR,GAAG;cACH6K,SAAS,EAAC,KAAK;cACf0C,GAAG,EAAErJ,QAAQ,CAACD,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAM;cACrC6E,EAAE,EAAE;gBACH0E,SAAS,EAAE,EAAAlL,uBAAA,GAAA1B,gBAAgB,CAAC2C,SAAS,CAAC,CAAC,CAAC,cAAAjB,uBAAA,wBAAAC,uBAAA,GAA7BD,uBAAA,CAA+BgL,eAAe,cAAA/K,uBAAA,uBAA9CA,uBAAA,CAAgDkL,cAAc,KAAI,MAAM;gBACnFC,SAAS,EAAE,EAAAlL,uBAAA,GAAA5B,gBAAgB,CAAC2C,SAAS,CAAC,CAAC,CAAC,cAAAf,uBAAA,wBAAAC,uBAAA,GAA7BD,uBAAA,CAA+B8K,eAAe,cAAA7K,uBAAA,wBAAAC,uBAAA,GAA9CD,uBAAA,CAAgDkL,cAAc,cAAAjL,uBAAA,wBAAAC,uBAAA,GAA9DD,uBAAA,CAAiE,CAAC,CAAC,cAAAC,uBAAA,uBAAnEA,uBAAA,CAAqEiL,GAAG,KAAI,SAAS;gBAChGpD,OAAO,EAAE,OAAO;gBAChBI,MAAM,EAAE;cACT;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAAC,GAAG,EACTrD,eAAe,iBACf3H,OAAA,CAACL,UAAU;UACX2I,EAAE,EAAE;YACH;YACFY,SAAS,EAAE,iCAAiC;YAC5CmE,UAAU,EAAE,KAAK;YACjB9D,UAAU,EAAE,iBAAiB;YAC7B+C,MAAM,EAAE,gBAAgB;YACxBhD,MAAM,EAAC,QAAQ;YACdqD,YAAY,EAAE,MAAM;YACrB1D,OAAO,EAAC;UAEP,CAAE;UAAAT,QAAA,eAEDxI,OAAA,CAACH,SAAS;YAAEyI,EAAE,EAAE;cAACgF,IAAI,EAAC,GAAG;cAAC9C,KAAK,EAAC;YAAM;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EACL;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAER,CAAC;AAACzK,EAAA,CA7XIN,aAAa;EAAA,QAKdH,cAAc,EAE2CA,cAAc;AAAA;AAAAyN,EAAA,GAPtEtN,aAAa;AA+XnB,eAAeA,aAAa;AAAC,IAAAsN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\CanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { defaultDots, topLeft, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowCanvasSettings,\n  selectedTemplate\n}) => {\n  _s();\n  const {\n    setCanvasSetting,\n    borderColor,\n    announcementJson,\n    width,\n    setWidth,\n    backgroundColor,\n    setBorderColor,\n    setBackgroundColor,\n    backgroundImage,\n    setBackgroundImage,\n    borderRadius,\n    setBorderRadius,\n    Annpadding,\n    setAnnPadding,\n    AnnborderSize,\n    setAnnBorderSize,\n    Bposition,\n    setBposition,\n    setIsUnSavedChanges,\n    currentStep,\n    syncAIAnnouncementCanvasSettings,\n    createWithAI,\n    //selectedTemplate,\n    selectedTemplateTour\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [isOpen, setIsOpen] = useState(true);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [widthError, setWidthError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [borderRadiusError, setBorderRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const positions = [{\n    label: translate(\"Top Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 41\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topcenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 43\n    }, this),\n    value: \"top-center\"\n  }, {\n    label: translate(\"Top Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 42\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 44\n    }, this),\n    value: \"left-center\"\n  }, {\n    label: translate(\"Middle Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 46\n    }, this),\n    value: \"center-center\"\n  }, {\n    label: translate(\"Middle Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 45\n    }, this),\n    value: \"right-center\"\n  }, {\n    label: translate(\"Bottom Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 44\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomMiddle\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 46\n    }, this),\n    value: \"bottom-center\"\n  }, {\n    label: translate(\"Bottom Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 45\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const renderPositionIcon = positionValue => {\n    const isSelected = Bposition === positionValue;\n    if (!isSelected) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: defaultDots\n        },\n        style: {\n          fontSize: \"small\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 12\n      }, this);\n    }\n    switch (positionValue) {\n      case \"top-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this);\n      case \"top-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topcenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this);\n      case \"top-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this);\n      case \"left-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this);\n      case \"center-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleCenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this);\n      case \"right-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      case \"bottom-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this);\n      case \"bottom-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomMiddle\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this);\n      case \"bottom-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: defaultDots\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  useEffect(() => {\n    var _announcementJson$Gui, _announcementJson$Gui2, _announcementJson$Gui3, _announcementJson$Gui4, _announcementJson$Gui5;\n    const currentStepIndex = announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui = announcementJson.GuideStep) === null || _announcementJson$Gui === void 0 ? void 0 : _announcementJson$Gui.findIndex(step => {\n      let stepNum = step.stepName; // \n      if (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\n        stepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\n      }\n      return String(stepNum) === String(currentStep);\n    });\n\n    // Get canvas data for the current step (if found), otherwise use the first step\n    const canvasData = currentStepIndex !== -1 ? announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui2 = announcementJson.GuideStep) === null || _announcementJson$Gui2 === void 0 ? void 0 : (_announcementJson$Gui3 = _announcementJson$Gui2[currentStepIndex]) === null || _announcementJson$Gui3 === void 0 ? void 0 : _announcementJson$Gui3.Canvas : announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui4 = announcementJson.GuideStep) === null || _announcementJson$Gui4 === void 0 ? void 0 : (_announcementJson$Gui5 = _announcementJson$Gui4[0]) === null || _announcementJson$Gui5 === void 0 ? void 0 : _announcementJson$Gui5.Canvas;\n    let initialWidth;\n    let initialPadding;\n    let initialBorderRadius;\n    let initialBorderSize;\n    if (canvasData) {\n      var _canvasData$Radius;\n      setSelectedPosition(canvasData.Position || \"center-center\");\n      setBposition(canvasData.Position || \"center-center\");\n      initialWidth = canvasData.Width || 500;\n      setBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\n      setBackgroundImage(canvasData.BackgroundImage || \"\");\n      initialBorderRadius = (_canvasData$Radius = canvasData.Radius) !== null && _canvasData$Radius !== void 0 ? _canvasData$Radius : 8;\n      initialPadding = canvasData.Padding || \"12\";\n      setBorderColor(canvasData.BorderColor || \"#000000\");\n      initialBorderSize = canvasData.BorderSize || 0;\n    } else {\n      setSelectedPosition(\"center-center\");\n      setBposition(\"center-center\");\n      initialWidth = 500;\n      setBackgroundColor(\"#ffffff\");\n      setBackgroundImage(\"\");\n      initialBorderRadius = 8;\n      initialPadding = \"12\";\n      setBorderColor(\"#000000\");\n      initialBorderSize = 0;\n    }\n\n    // Validate initial width\n    if (initialWidth < 300 || initialWidth > 1200) {\n      setWidthError(true);\n      // Set width to closest valid value\n      setWidth(initialWidth < 300 ? 300 : 1200);\n    } else {\n      setWidthError(false);\n      setWidth(initialWidth);\n    }\n\n    // Validate initial padding\n    const paddingValue = parseInt(initialPadding) || 12;\n    if (paddingValue < 0 || paddingValue > 20) {\n      setPaddingError(true);\n      // Set padding to closest valid value\n      setAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\n    } else {\n      setPaddingError(false);\n      setAnnPadding(initialPadding);\n    }\n\n    // Validate initial border radius\n    if (initialBorderRadius < 0 || initialBorderRadius > 20) {\n      setBorderRadiusError(true);\n      // Set border radius to closest valid value\n      setBorderRadius(initialBorderRadius < 0 ? 0 : 20);\n    } else {\n      setBorderRadiusError(false);\n      setBorderRadius(initialBorderRadius);\n    }\n\n    // Validate initial border size\n    if (initialBorderSize < 0 || initialBorderSize > 10) {\n      setBorderSizeError(true);\n      // Set border size to closest valid value\n      setAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\n    } else {\n      setBorderSizeError(false);\n      setAnnBorderSize(initialBorderSize);\n    }\n  }, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBackgroundImage, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\n  const handlePositionClick = positionValue => {\n    setSelectedPosition(positionValue);\n    setBposition(positionValue);\n  };\n  const handleBorderColorChange = e => setBorderColor(e.target.value);\n  const handleBackgroundColorChange = e => setBackgroundColor(e.target.value);\n  const handleBackgroundImageUpload = e => {\n    const file = e.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = event => {\n        setBackgroundImage(event.target.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleRemoveBackgroundImage = () => {\n    setBackgroundImage(\"\");\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    // Don't apply changes if there's any validation error\n    if (widthError || paddingError || borderRadiusError || borderSizeError) {\n      return;\n    }\n    const canvasData = {\n      Position: selectedPosition,\n      BackgroundColor: backgroundColor,\n      Width: width || 500,\n      Radius: borderRadius !== undefined ? borderRadius : 0,\n      Padding: Annpadding || \"12\",\n      BorderColor: borderColor,\n      BorderSize: AnnborderSize || 0,\n      Zindex: 9999\n    };\n    setCanvasSetting(canvasData);\n\n    // Sync AI announcement canvas settings after applying changes\n    if (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\n      // Use setTimeout to ensure setCanvasSetting completes first\n      setTimeout(() => {\n        syncAIAnnouncementCanvasSettings(canvasData);\n      }, 0);\n    }\n    setBposition(selectedPosition);\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Canvas\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls qadpt-errmsg\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-position-grid\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-ctrl-title\",\n                children: translate(\"Position\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                id: \"pos-container\",\n                container: true,\n                spacing: 1\n                //onClick={handlePositionClick}\n                ,\n                children: positions.map(position => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 4,\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    disableRipple: true\n                    // sx={{\n                    //     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\n                    // }}\n                    ,\n                    onClick: () => handlePositionClick(position.value) // Pass value directly\n                    ,\n                    children: renderPositionIcon(position.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 9\n                  }, this)\n                }, position.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 8\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 6\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${width}`,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate width between 300px and 1200px\n                    if (inputValue < 300 || inputValue > 1200) {\n                      setWidthError(true);\n                    } else {\n                      setWidthError(false);\n                    }\n                    setWidth(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: widthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 6\n            }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 10\n              }, this), translate(\"Value must be between 300px and 1200px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Padding\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${Annpadding}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate padding between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setPaddingError(true);\n                    } else {\n                      setPaddingError(false);\n                    }\n                    setAnnPadding(inputValue.toString());\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: paddingError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 6\n            }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Radius\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${borderRadius}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border radius between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setBorderRadiusError(true);\n                      // Set border radius to closest valid value\n                      // setBorderRadius(inputValue < 0 ? 0 : 20);\n                    } else {\n                      setBorderRadiusError(false);\n                    }\n                    setBorderRadius(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderRadiusError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 6\n            }, this), borderRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Size\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${AnnborderSize}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border size between 0px and 10px\n                    if (inputValue < 0 || inputValue > 10) {\n                      setBorderSizeError(true);\n                    } else {\n                      setBorderSizeError(false);\n                    }\n                    setAnnBorderSize(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderSizeError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 6\n            }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 10px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: borderColor,\n                  onChange: handleBorderColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Background\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: backgroundColor,\n                  onChange: handleBackgroundColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 6\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: `qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n            disabled: widthError || paddingError || borderRadiusError || borderSizeError,\n            children: translate(\"Apply\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(CanvasSettings, \"a2noZnn8JqBfPGlRncOz9o4euqg=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = CanvasSettings;\nexport default CanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"CanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "defaultDots", "topLeft", "topRight", "middleLeft", "middleCenter", "middleRight", "bottomLeft", "bottomMiddle", "bottomRight", "topcenter", "warning", "centercenter", "useTranslation", "jsxDEV", "_jsxDEV", "CanvasSettings", "zindeex", "setZindeex", "setShowCanvasSettings", "selectedTemplate", "_s", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "backgroundImage", "setBackgroundImage", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "setIsUnSavedChanges", "currentStep", "syncAIAnnouncementCanvasSettings", "createWithAI", "selectedTemplateTour", "state", "t", "translate", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "widthError", "setWidthError", "paddingError", "setPaddingError", "borderRadiusError", "setBorderRadiusError", "borderSizeError", "setBorderSizeError", "positions", "label", "icon", "dangerouslySetInnerHTML", "__html", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "renderPositionIcon", "positionValue", "isSelected", "_announcementJson$Gui", "_announcementJson$Gui2", "_announcementJson$Gui3", "_announcementJson$Gui4", "_announcementJson$Gui5", "currentStepIndex", "GuideStep", "findIndex", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "toLowerCase", "startsWith", "parseInt", "replace", "String", "canvasData", "<PERSON><PERSON>", "initialWidth", "initialPadding", "initialBorderRadius", "initialBorderSize", "_canvasData$Radius", "Position", "<PERSON><PERSON><PERSON>", "BackgroundColor", "BackgroundImage", "<PERSON><PERSON>", "Padding", "BorderColor", "BorderSize", "paddingValue", "handlePositionClick", "handleBorderColorChange", "e", "target", "handleBackgroundColorChange", "handleBackgroundImageUpload", "file", "files", "reader", "FileReader", "onload", "event", "result", "readAsDataURL", "handleRemoveBackgroundImage", "handleClose", "handleApplyChanges", "undefined", "Zindex", "setTimeout", "id", "className", "children", "onClick", "size", "container", "spacing", "map", "position", "item", "xs", "disable<PERSON><PERSON><PERSON>", "variant", "autoFocus", "onChange", "inputValue", "InputProps", "endAdornment", "sx", "border", "error", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "fullWidth", "toString", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/CanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots,topLeft,topCenter,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst CanvasSettings = ({ zindeex, setZindeex, setShowCanvasSettings, selectedTemplate }: any) => {\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundImage,\r\n\t\tsetBackgroundImage,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tcurrentStep,\r\n\t\tsyncAIAnnouncementCanvasSettings,\r\n\t\tcreateWithAI,\r\n\t\t//selectedTemplate,\r\n\t\tselectedTemplateTour\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderRadiusError, setBorderRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\"), icon: <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\"), icon: <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\"), icon: <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\"), icon: <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />, value: \"left-center\" },\r\n\t\t{ label: translate(\"Middle Center\"), icon: <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />, value: \"center-center\" },\r\n\t\t{ label: translate(\"Middle Right\"), icon: <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />, value: \"right-center\" },\r\n\t\t{ label: translate(\"Bottom Left\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />, value: \"bottom-right\" },\r\n\t];\r\n\tconst renderPositionIcon = (positionValue:any) => {\r\n\t\tconst isSelected = Bposition === positionValue;\r\n\r\n\t\tif (!isSelected) {\r\n\t\t  return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\r\n\t\tswitch (positionValue) {\r\n\t\t  case \"top-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"left-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"center-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"right-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  default:\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\t  };\r\n\r\n\tuseEffect(() => {\r\n\t\tconst currentStepIndex = announcementJson?.GuideStep?.findIndex(\r\n\t\t\t(step: any) => {\r\n\t\t\t\tlet stepNum = step.stepName;// \r\n\t\t\t\tif (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\r\n\t\t\t\t\tstepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\r\n\t\t\t\t}\r\n\t\t\t\treturn String(stepNum) === String(currentStep); \r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Get canvas data for the current step (if found), otherwise use the first step\r\n\t\tconst canvasData = (currentStepIndex !== -1)\r\n\t\t\t? announcementJson?.GuideStep?.[currentStepIndex]?.Canvas\r\n\t\t\t: announcementJson?.GuideStep?.[0]?.Canvas;\r\n\r\n\t\tlet initialWidth;\r\n\t\tlet initialPadding;\r\n\t\tlet initialBorderRadius;\r\n\t\tlet initialBorderSize;\r\n\r\n\t\tif (canvasData) {\r\n\t\t\tsetSelectedPosition(canvasData.Position || \"center-center\");\r\n\t\t\tsetBposition(canvasData.Position || \"center-center\");\r\n\t\t\tinitialWidth = canvasData.Width || 500;\r\n\t\t\tsetBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\r\n\t\t\tsetBackgroundImage(canvasData.BackgroundImage || \"\");\r\n\t\t\tinitialBorderRadius = canvasData.Radius ?? 8;\r\n\t\t\tinitialPadding = canvasData.Padding || \"12\";\r\n\t\t\tsetBorderColor(canvasData.BorderColor || \"#000000\");\r\n\t\t\tinitialBorderSize = canvasData.BorderSize || 0;\r\n\t\t} else {\r\n\t\t\tsetSelectedPosition(\"center-center\");\r\n\t\t\tsetBposition(\"center-center\");\r\n\t\t\tinitialWidth = 500;\r\n\t\t\tsetBackgroundColor(\"#ffffff\");\r\n\t\t\tsetBackgroundImage(\"\");\r\n\t\t\tinitialBorderRadius = 8;\r\n\t\t\tinitialPadding = \"12\";\r\n\t\t\tsetBorderColor(\"#000000\");\r\n\t\t\tinitialBorderSize = 0;\r\n\t\t}\r\n\r\n\t\t// Validate initial width\r\n\t\tif (initialWidth < 300 || initialWidth > 1200) {\r\n\t\t\tsetWidthError(true);\r\n\t\t\t// Set width to closest valid value\r\n\t\t\tsetWidth(initialWidth < 300 ? 300 : 1200);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t\tsetWidth(initialWidth);\r\n\t\t}\r\n\r\n\t\t// Validate initial padding\r\n\t\tconst paddingValue = parseInt(initialPadding) || 12;\r\n\t\tif (paddingValue < 0 || paddingValue > 20) {\r\n\t\t\tsetPaddingError(true);\r\n\t\t\t// Set padding to closest valid value\r\n\t\t\tsetAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\r\n\t\t} else {\r\n\t\t\tsetPaddingError(false);\r\n\t\t\tsetAnnPadding(initialPadding);\r\n\t\t}\r\n\r\n\t\t// Validate initial border radius\r\n\t\tif (initialBorderRadius < 0 || initialBorderRadius > 20) {\r\n\t\t\tsetBorderRadiusError(true);\r\n\t\t\t// Set border radius to closest valid value\r\n\t\t\tsetBorderRadius(initialBorderRadius < 0 ? 0 : 20);\r\n\t\t} else {\r\n\t\t\tsetBorderRadiusError(false);\r\n\t\t\tsetBorderRadius(initialBorderRadius);\r\n\t\t}\r\n\r\n\t\t// Validate initial border size\r\n\t\tif (initialBorderSize < 0 || initialBorderSize > 10) {\r\n\t\t\tsetBorderSizeError(true);\r\n\t\t\t// Set border size to closest valid value\r\n\t\t\tsetAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\r\n\t\t} else {\r\n\t\t\tsetBorderSizeError(false);\r\n\t\t\tsetAnnBorderSize(initialBorderSize);\r\n\t\t}\r\n\t}, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBackgroundImage, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\r\n\r\n\tconst handlePositionClick = (positionValue: string) => {\r\n\t\tsetSelectedPosition(positionValue);\r\n\t\tsetBposition(positionValue);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleBackgroundImageUpload = (e: any) => {\r\n\t\tconst file = e.target.files[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onload = (event: any) => {\r\n\t\t\t\tsetBackgroundImage(event.target.result);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleRemoveBackgroundImage = () => {\r\n\t\tsetBackgroundImage(\"\");\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || borderRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst canvasData = {\r\n\t\t\tPosition: selectedPosition,\r\n\t\t\tBackgroundColor: backgroundColor,\r\n\t\t\tWidth: width || 500,\r\n\t\t\tRadius: borderRadius !== undefined ? borderRadius : 0,\r\n\t\t\tPadding: Annpadding || \"12\",\r\n\t\t\tBorderColor: borderColor,\r\n\t\t\tBorderSize: AnnborderSize || 0,\r\n\t\t\tZindex: 9999,\r\n\t\t};\r\n\r\n\t\tsetCanvasSetting(canvasData);\r\n\r\n\t\t// Sync AI announcement canvas settings after applying changes\r\n\t\tif (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\r\n\t\t\t// Use setTimeout to ensure setCanvasSetting completes first\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsyncAIAnnouncementCanvasSettings(canvasData);\r\n\t\t\t}, 0);\r\n\t\t}\r\n\r\n\t\tsetBposition(selectedPosition);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t<Grid\r\n\t\t\t\t\t\tid=\"pos-container\"\r\n\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\tspacing={1}\r\n\t\t\t\t\t\t//onClick={handlePositionClick}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Position Icons */}\r\n\t\t\t\t\t\t{positions.map((position) => (\r\n\t\t\t\t\t\t\t<Grid item xs={4} key={position.value}>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tdisableRipple\r\n\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t//     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handlePositionClick(position.value)} // Pass value directly\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{renderPositionIcon(position.value)}\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t{/* Width Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Width\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${width}`}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetWidth(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 300px and 1200px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Height</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${height}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetHeight(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Padding Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${Annpadding}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnPadding(inputValue.toString());\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Border Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Radius\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${borderRadius}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border radius between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t// Set border radius to closest valid value\r\n\t\t\t\t\t\t\t\t\t// setBorderRadius(inputValue < 0 ? 0 : 20);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetBorderRadius(inputValue);\r\n\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\terror={borderRadiusError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${AnnborderSize}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnBorderSize(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 10px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Zindex value Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${zindeex}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetZindeex(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Border Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={borderColor}\r\n\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* Background Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundColor}\r\n\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={widthError || paddingError || borderRadiusError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default CanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAiB,eAAe;AAC7F,OAAOC,SAAS,MAAM,2BAA2B;AAGjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,WAAW,EAACC,OAAO,EAAWC,QAAQ,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,QAAQ,0BAA0B;AAC3L,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,qBAAqB;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACjG,MAAM;IACLC,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,cAAc;IACdC,kBAAkB;IAClBC,eAAe;IACfC,kBAAkB;IAClBC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,mBAAmB;IACnBC,WAAW;IACXC,gCAAgC;IAChCC,YAAY;IACZ;IACAC;EACD,CAAC,GAAG5C,cAAc,CAAE6C,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGlC,cAAc,CAAC,CAAC;EACzC,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMqE,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEd,SAAS,CAAC,UAAU,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE9D;MAAQ,CAAE;MAAC+D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EAC/I;IAAEV,KAAK,EAAEd,SAAS,CAAC,YAAY,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEtD;MAAU,CAAE;MAACuD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAa,CAAC,EACrJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,WAAW,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE7D;MAAS,CAAE;MAAC8D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAClJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,aAAa,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE5D;MAAW,CAAE;MAAC6D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,eAAe,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE3D;MAAa,CAAE;MAAC4D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEd,SAAS,CAAC,cAAc,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE1D;MAAY,CAAE;MAAC2D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC3J;IAAEV,KAAK,EAAEd,SAAS,CAAC,aAAa,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEzD;MAAW,CAAE;MAAC0D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,eAAe,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAExD;MAAa,CAAE;MAACyD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEd,SAAS,CAAC,cAAc,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEvD;MAAY,CAAE;MAACwD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CAC3J;EACD,MAAMC,kBAAkB,GAAIC,aAAiB,IAAK;IACjD,MAAMC,UAAU,GAAGpC,SAAS,KAAKmC,aAAa;IAE9C,IAAI,CAACC,UAAU,EAAE;MACf,oBAAO3D,OAAA;QAAMgD,uBAAuB,EAAE;UAAEC,MAAM,EAAE/D;QAAY,CAAE;QAACgE,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjG;IAEA,QAAQG,aAAa;MACnB,KAAK,UAAU;QAChB,oBAAO1D,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE9D;UAAQ,CAAE;UAAC+D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1F,KAAK,YAAY;QAClB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEtD;UAAU,CAAE;UAACuD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5F,KAAK,WAAW;QACjB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE7D;UAAS,CAAE;UAAC8D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,aAAa;QACnB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE5D;UAAW,CAAE;UAAC6D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE3D;UAAa,CAAE;UAAC4D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE1D;UAAY,CAAE;UAAC2D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F,KAAK,aAAa;QACnB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEzD;UAAW,CAAE;UAAC0D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAExD;UAAa,CAAE;UAACyD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEvD;UAAY,CAAE;UAACwD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F;QACD,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE/D;UAAY,CAAE;UAACgE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAChG;EACC,CAAC;EAEHhF,SAAS,CAAC,MAAM;IAAA,IAAAqF,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACf,MAAMC,gBAAgB,GAAGxD,gBAAgB,aAAhBA,gBAAgB,wBAAAmD,qBAAA,GAAhBnD,gBAAgB,CAAEyD,SAAS,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,SAAS,CAC7DC,IAAS,IAAK;MACd,IAAIC,OAAO,GAAGD,IAAI,CAACE,QAAQ,CAAC;MAC5B,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACE,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC5EH,OAAO,GAAGI,QAAQ,CAACJ,OAAO,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACvD;MACA,OAAOC,MAAM,CAACN,OAAO,CAAC,KAAKM,MAAM,CAACjD,WAAW,CAAC;IAC/C,CACD,CAAC;;IAED;IACA,MAAMkD,UAAU,GAAIX,gBAAgB,KAAK,CAAC,CAAC,GACxCxD,gBAAgB,aAAhBA,gBAAgB,wBAAAoD,sBAAA,GAAhBpD,gBAAgB,CAAEyD,SAAS,cAAAL,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8BI,gBAAgB,CAAC,cAAAH,sBAAA,uBAA/CA,sBAAA,CAAiDe,MAAM,GACvDpE,gBAAgB,aAAhBA,gBAAgB,wBAAAsD,sBAAA,GAAhBtD,gBAAgB,CAAEyD,SAAS,cAAAH,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,uBAAhCA,sBAAA,CAAkCa,MAAM;IAE3C,IAAIC,YAAY;IAChB,IAAIC,cAAc;IAClB,IAAIC,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIL,UAAU,EAAE;MAAA,IAAAM,kBAAA;MACf9C,mBAAmB,CAACwC,UAAU,CAACO,QAAQ,IAAI,eAAe,CAAC;MAC3D3D,YAAY,CAACoD,UAAU,CAACO,QAAQ,IAAI,eAAe,CAAC;MACpDL,YAAY,GAAGF,UAAU,CAACQ,KAAK,IAAI,GAAG;MACtCtE,kBAAkB,CAAC8D,UAAU,CAACS,eAAe,IAAI,SAAS,CAAC;MAC3DrE,kBAAkB,CAAC4D,UAAU,CAACU,eAAe,IAAI,EAAE,CAAC;MACpDN,mBAAmB,IAAAE,kBAAA,GAAGN,UAAU,CAACW,MAAM,cAAAL,kBAAA,cAAAA,kBAAA,GAAI,CAAC;MAC5CH,cAAc,GAAGH,UAAU,CAACY,OAAO,IAAI,IAAI;MAC3C3E,cAAc,CAAC+D,UAAU,CAACa,WAAW,IAAI,SAAS,CAAC;MACnDR,iBAAiB,GAAGL,UAAU,CAACc,UAAU,IAAI,CAAC;IAC/C,CAAC,MAAM;MACNtD,mBAAmB,CAAC,eAAe,CAAC;MACpCZ,YAAY,CAAC,eAAe,CAAC;MAC7BsD,YAAY,GAAG,GAAG;MAClBhE,kBAAkB,CAAC,SAAS,CAAC;MAC7BE,kBAAkB,CAAC,EAAE,CAAC;MACtBgE,mBAAmB,GAAG,CAAC;MACvBD,cAAc,GAAG,IAAI;MACrBlE,cAAc,CAAC,SAAS,CAAC;MACzBoE,iBAAiB,GAAG,CAAC;IACtB;;IAEA;IACA,IAAIH,YAAY,GAAG,GAAG,IAAIA,YAAY,GAAG,IAAI,EAAE;MAC9CxC,aAAa,CAAC,IAAI,CAAC;MACnB;MACA3B,QAAQ,CAACmE,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;IAC1C,CAAC,MAAM;MACNxC,aAAa,CAAC,KAAK,CAAC;MACpB3B,QAAQ,CAACmE,YAAY,CAAC;IACvB;;IAEA;IACA,MAAMa,YAAY,GAAGlB,QAAQ,CAACM,cAAc,CAAC,IAAI,EAAE;IACnD,IAAIY,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MAC1CnD,eAAe,CAAC,IAAI,CAAC;MACrB;MACApB,aAAa,CAACuE,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7C,CAAC,MAAM;MACNnD,eAAe,CAAC,KAAK,CAAC;MACtBpB,aAAa,CAAC2D,cAAc,CAAC;IAC9B;;IAEA;IACA,IAAIC,mBAAmB,GAAG,CAAC,IAAIA,mBAAmB,GAAG,EAAE,EAAE;MACxDtC,oBAAoB,CAAC,IAAI,CAAC;MAC1B;MACAxB,eAAe,CAAC8D,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC,MAAM;MACNtC,oBAAoB,CAAC,KAAK,CAAC;MAC3BxB,eAAe,CAAC8D,mBAAmB,CAAC;IACrC;;IAEA;IACA,IAAIC,iBAAiB,GAAG,CAAC,IAAIA,iBAAiB,GAAG,EAAE,EAAE;MACpDrC,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAtB,gBAAgB,CAAC2D,iBAAiB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC,MAAM;MACNrC,kBAAkB,CAAC,KAAK,CAAC;MACzBtB,gBAAgB,CAAC2D,iBAAiB,CAAC;IACpC;EACD,CAAC,EAAE,CAACxE,gBAAgB,EAAEiB,WAAW,EAAEF,YAAY,EAAEb,QAAQ,EAAEG,kBAAkB,EAAEE,kBAAkB,EAAEE,eAAe,EAAEE,aAAa,EAAEP,cAAc,EAAES,gBAAgB,CAAC,CAAC;EAErK,MAAMsE,mBAAmB,GAAIlC,aAAqB,IAAK;IACtDtB,mBAAmB,CAACsB,aAAa,CAAC;IAClClC,YAAY,CAACkC,aAAa,CAAC;EAC5B,CAAC;EACD,MAAMmC,uBAAuB,GAAIC,CAAM,IAAKjF,cAAc,CAACiF,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;EAC1E,MAAMwC,2BAA2B,GAAIF,CAAM,IAAKhF,kBAAkB,CAACgF,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;EAElF,MAAMyC,2BAA2B,GAAIH,CAAM,IAAK;IAC/C,MAAMI,IAAI,GAAGJ,CAAC,CAACC,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACT,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAU,IAAK;QAC/BvF,kBAAkB,CAACuF,KAAK,CAACR,MAAM,CAACS,MAAM,CAAC;MACxC,CAAC;MACDJ,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMQ,2BAA2B,GAAGA,CAAA,KAAM;IACzC1F,kBAAkB,CAAC,EAAE,CAAC;EACvB,CAAC;EAED,MAAM2F,WAAW,GAAGA,CAAA,KAAM;IACzBzE,SAAS,CAAC,KAAK,CAAC;IAChB9B,qBAAqB,CAAC,KAAK,CAAC;EAC7B,CAAC;EACD,MAAMwG,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,IAAIvE,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,EAAE;MACvE;IACD;IAEA,MAAMiC,UAAU,GAAG;MAClBO,QAAQ,EAAEhD,gBAAgB;MAC1BkD,eAAe,EAAEzE,eAAe;MAChCwE,KAAK,EAAE1E,KAAK,IAAI,GAAG;MACnB6E,MAAM,EAAEtE,YAAY,KAAK4F,SAAS,GAAG5F,YAAY,GAAG,CAAC;MACrDuE,OAAO,EAAErE,UAAU,IAAI,IAAI;MAC3BsE,WAAW,EAAEjF,WAAW;MACxBkF,UAAU,EAAErE,aAAa,IAAI,CAAC;MAC9ByF,MAAM,EAAE;IACT,CAAC;IAEDvG,gBAAgB,CAACqE,UAAU,CAAC;;IAE5B;IACA,IAAIhD,YAAY,KAAKvB,gBAAgB,KAAK,cAAc,IAAIwB,oBAAoB,KAAK,cAAc,CAAC,EAAE;MACrG;MACAkF,UAAU,CAAC,MAAM;QAChBpF,gCAAgC,CAACiD,UAAU,CAAC;MAC7C,CAAC,EAAE,CAAC,CAAC;IACN;IAEApD,YAAY,CAACW,gBAAgB,CAAC;IAC9BwE,WAAW,CAAC,CAAC;IACblF,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACAjC,OAAA;MACCgH,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7BlH,OAAA;QAAKiH,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7BlH,OAAA;UAAKiH,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnClH,OAAA,CAACnB,UAAU;YACV,cAAW,OAAO;YAClBsI,OAAO,EAAER,WAAY;YAAAO,QAAA,eAErBlH,OAAA,CAAChB,2BAA2B;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CAAC,eACbvD,OAAA;YAAKiH,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAElF,SAAS,CAAC,QAAQ;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExDvD,OAAA,CAACnB,UAAU;YACVuI,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBD,OAAO,EAAER,WAAY;YAAAO,QAAA,eAErBlH,OAAA,CAACjB,SAAS;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNvD,OAAA;UAAKiH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC/BlH,OAAA;YAAKiH,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC5ClH,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBACjClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,UAAU;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EvD,OAAA,CAACpB,IAAI;gBACJoI,EAAE,EAAC,eAAe;gBAClBK,SAAS;gBACTC,OAAO,EAAE;gBACT;gBAAA;gBAAAJ,QAAA,EAGCrE,SAAS,CAAC0E,GAAG,CAAEC,QAAQ,iBACvBxH,OAAA,CAACpB,IAAI;kBAAC6I,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAR,QAAA,eAChBlH,OAAA,CAACnB,UAAU;oBACVuI,IAAI,EAAC,OAAO;oBACZO,aAAa;oBACb;oBACA;oBACA;oBAAA;oBACAR,OAAO,EAAEA,CAAA,KAAMvB,mBAAmB,CAAC4B,QAAQ,CAAChE,KAAK,CAAE,CAAC;oBAAA;oBAAA0D,QAAA,EAEnDzD,kBAAkB,CAAC+D,QAAQ,CAAChE,KAAK;kBAAC;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC,GAVSiE,QAAQ,CAAChE,KAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW/B,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAILvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA;gBAAKiH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,OAAO;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA,CAACrB,SAAS;kBACTiJ,OAAO,EAAC,UAAU;kBAClBpE,KAAK,EAAE,GAAG9C,KAAK,EAAG;kBAClB0G,IAAI,EAAC,OAAO;kBACZS,SAAS;kBACTZ,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGhC,CAAC,IAAK;oBAChB,MAAMiC,UAAU,GAAGtD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIuE,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,IAAI,EAAE;sBAC1CzF,aAAa,CAAC,IAAI,CAAC;oBACpB,CAAC,MAAM;sBACNA,aAAa,CAAC,KAAK,CAAC;oBACrB;oBAEA3B,QAAQ,CAACoH,UAAU,CAAC;kBACrB,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE/F;gBAAW;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF,CAAC,EACLlB,UAAU,iBACXrC,OAAA,CAACtB,UAAU;cACVwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBkF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAC9I;cACZ,CAAE;cAAAqH,QAAA,gBACAlH,OAAA;gBAAMkD,KAAK,EAAE;kBAAEwF,OAAO,EAAE,MAAM;kBAAEvF,QAAQ,EAAE,MAAM;kBAAEwF,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F5F,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACCvB,SAAS,CAAC,yCAAyC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACZ,eAuBDvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,SAAS;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA,CAACrB,SAAS;kBACTiJ,OAAO,EAAC,UAAU;kBAClBpE,KAAK,EAAE,GAAGrC,UAAU,EAAG;kBACvB0H,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGhC,CAAC,IAAK;oBAChB,MAAMiC,UAAU,GAAGtD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIuE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCvF,eAAe,CAAC,IAAI,CAAC;oBACtB,CAAC,MAAM;sBACNA,eAAe,CAAC,KAAK,CAAC;oBACvB;oBAEApB,aAAa,CAAC2G,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC;kBACrC,CAAE;kBACFd,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE7F;gBAAa;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLhB,YAAY,iBACZvC,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBkF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAC9I;cACZ,CAAE;cAAAqH,QAAA,gBACAlH,OAAA;gBAAMkD,KAAK,EAAE;kBAAEwF,OAAO,EAAE,MAAM;kBAAEvF,QAAQ,EAAE,MAAM;kBAAEwF,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F5F,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAGDvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,eAAe;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrFvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA,CAACrB,SAAS;kBACTiJ,OAAO,EAAC,UAAU;kBAClBpE,KAAK,EAAE,GAAGvC,YAAY,EAAG;kBACzB4H,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGhC,CAAC,IAAK;oBAChB,MAAMiC,UAAU,GAAGtD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIuE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCrF,oBAAoB,CAAC,IAAI,CAAC;sBAC1B;sBACA;oBACD,CAAC,MAAM;sBACNA,oBAAoB,CAAC,KAAK,CAAC;oBAC5B;oBACCxB,eAAe,CAAC6G,UAAU,CAAC;kBAE7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACC,CAAE;kBACFC,KAAK,EAAE3F;gBAAkB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACLd,iBAAiB,iBACjBzC,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBkF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAC9I;cACZ,CAAE;cAAAqH,QAAA,gBACAlH,OAAA;gBAAMkD,KAAK,EAAE;kBAAEwF,OAAO,EAAE,MAAM;kBAAEvF,QAAQ,EAAE,MAAM;kBAAEwF,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F5F,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eACDvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,aAAa;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnFvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA,CAACrB,SAAS;kBACTiJ,OAAO,EAAC,UAAU;kBAClBpE,KAAK,EAAE,GAAGnC,aAAa,EAAG;kBAC1BwH,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGhC,CAAC,IAAK;oBAChB,MAAMiC,UAAU,GAAGtD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIuE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCnF,kBAAkB,CAAC,IAAI,CAAC;oBACzB,CAAC,MAAM;sBACNA,kBAAkB,CAAC,KAAK,CAAC;oBAC1B;oBAEAtB,gBAAgB,CAACyG,UAAU,CAAC;kBAC7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAEzF;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLZ,eAAe,iBACf3C,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBkF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAC9I;cACZ,CAAE;cAAAqH,QAAA,gBACAlH,OAAA;gBAAMkD,KAAK,EAAE;kBAAEwF,OAAO,EAAE,MAAM;kBAAEvF,QAAQ,EAAE,MAAM;kBAAEwF,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F5F,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAkBDvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,QAAQ;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9EvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA;kBACC+I,IAAI,EAAC,OAAO;kBACZvF,KAAK,EAAEhD,WAAY;kBACnBsH,QAAQ,EAAEjC,uBAAwB;kBAClCoB,SAAS,EAAC;gBAAmB;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNvD,OAAA,CAACvB,GAAG;cAACwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClH,OAAA,CAACtB,UAAU;gBAACuI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAElF,SAAS,CAAC,YAAY;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClFvD,OAAA;gBAAAkH,QAAA,eACDlH,OAAA;kBACC+I,IAAI,EAAC,OAAO;kBACZvF,KAAK,EAAE5C,eAAgB;kBACvBkH,QAAQ,EAAE9B,2BAA4B;kBACtCiB,SAAS,EAAC;gBAAmB;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPvD,OAAA;UAAKiH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjClH,OAAA,CAAClB,MAAM;YACN8I,OAAO,EAAC,WAAW;YACnBT,OAAO,EAAEP,kBAAmB;YAC5BK,SAAS,EAAE,aAAa5E,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/GqG,QAAQ,EAAE3G,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB;YAAAuE,QAAA,EAE7ElF,SAAS,CAAC,OAAO;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF;IACL;EAAA;AAEF,CAAC;AAACjD,EAAA,CAzkBIL,cAAc;EAAA,QA0BfhB,cAAc,EACOa,cAAc;AAAA;AAAAmJ,EAAA,GA3BlChJ,cAAc;AA2kBpB,eAAeA,cAAc;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
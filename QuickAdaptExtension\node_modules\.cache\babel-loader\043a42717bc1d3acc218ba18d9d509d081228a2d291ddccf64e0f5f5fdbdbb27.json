{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\CanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { defaultDots, topLeft, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowCanvasSettings,\n  selectedTemplate\n}) => {\n  _s();\n  const {\n    setCanvasSetting,\n    borderColor,\n    announcementJson,\n    width,\n    setWidth,\n    backgroundColor,\n    setBorderColor,\n    setBackgroundColor,\n    backgroundImage,\n    setBackgroundImage,\n    borderRadius,\n    setBorderRadius,\n    Annpadding,\n    setAnnPadding,\n    AnnborderSize,\n    setAnnBorderSize,\n    Bposition,\n    setBposition,\n    setIsUnSavedChanges,\n    currentStep,\n    syncAIAnnouncementCanvasSettings,\n    createWithAI,\n    //selectedTemplate,\n    selectedTemplateTour\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [isOpen, setIsOpen] = useState(true);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [widthError, setWidthError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [borderRadiusError, setBorderRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const [imagePreview, setImagePreview] = useState(\"\");\n  const positions = [{\n    label: translate(\"Top Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 41\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topcenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 43\n    }, this),\n    value: \"top-center\"\n  }, {\n    label: translate(\"Top Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 42\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 44\n    }, this),\n    value: \"left-center\"\n  }, {\n    label: translate(\"Middle Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 46\n    }, this),\n    value: \"center-center\"\n  }, {\n    label: translate(\"Middle Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 45\n    }, this),\n    value: \"right-center\"\n  }, {\n    label: translate(\"Bottom Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 44\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomMiddle\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 46\n    }, this),\n    value: \"bottom-center\"\n  }, {\n    label: translate(\"Bottom Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 45\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const renderPositionIcon = positionValue => {\n    const isSelected = Bposition === positionValue;\n    if (!isSelected) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: defaultDots\n        },\n        style: {\n          fontSize: \"small\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 12\n      }, this);\n    }\n    switch (positionValue) {\n      case \"top-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this);\n      case \"top-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topcenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this);\n      case \"top-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this);\n      case \"left-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this);\n      case \"center-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleCenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this);\n      case \"right-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this);\n      case \"bottom-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this);\n      case \"bottom-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomMiddle\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this);\n      case \"bottom-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: defaultDots\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  useEffect(() => {\n    var _announcementJson$Gui, _announcementJson$Gui2, _announcementJson$Gui3, _announcementJson$Gui4, _announcementJson$Gui5;\n    const currentStepIndex = announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui = announcementJson.GuideStep) === null || _announcementJson$Gui === void 0 ? void 0 : _announcementJson$Gui.findIndex(step => {\n      let stepNum = step.stepName; // \n      if (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\n        stepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\n      }\n      return String(stepNum) === String(currentStep);\n    });\n\n    // Get canvas data for the current step (if found), otherwise use the first step\n    const canvasData = currentStepIndex !== -1 ? announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui2 = announcementJson.GuideStep) === null || _announcementJson$Gui2 === void 0 ? void 0 : (_announcementJson$Gui3 = _announcementJson$Gui2[currentStepIndex]) === null || _announcementJson$Gui3 === void 0 ? void 0 : _announcementJson$Gui3.Canvas : announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui4 = announcementJson.GuideStep) === null || _announcementJson$Gui4 === void 0 ? void 0 : (_announcementJson$Gui5 = _announcementJson$Gui4[0]) === null || _announcementJson$Gui5 === void 0 ? void 0 : _announcementJson$Gui5.Canvas;\n    let initialWidth;\n    let initialPadding;\n    let initialBorderRadius;\n    let initialBorderSize;\n    if (canvasData) {\n      var _canvasData$Radius;\n      setSelectedPosition(canvasData.Position || \"center-center\");\n      setBposition(canvasData.Position || \"center-center\");\n      initialWidth = canvasData.Width || 500;\n      setBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\n      setBackgroundImage(canvasData.BackgroundImage || \"\");\n      setImagePreview(canvasData.BackgroundImage || \"\");\n      initialBorderRadius = (_canvasData$Radius = canvasData.Radius) !== null && _canvasData$Radius !== void 0 ? _canvasData$Radius : 8;\n      initialPadding = canvasData.Padding || \"12\";\n      setBorderColor(canvasData.BorderColor || \"#000000\");\n      initialBorderSize = canvasData.BorderSize || 0;\n    } else {\n      setSelectedPosition(\"center-center\");\n      setBposition(\"center-center\");\n      initialWidth = 500;\n      setBackgroundColor(\"#ffffff\");\n      initialBorderRadius = 8;\n      initialPadding = \"12\";\n      setBorderColor(\"#000000\");\n      initialBorderSize = 0;\n    }\n\n    // Validate initial width\n    if (initialWidth < 300 || initialWidth > 1200) {\n      setWidthError(true);\n      // Set width to closest valid value\n      setWidth(initialWidth < 300 ? 300 : 1200);\n    } else {\n      setWidthError(false);\n      setWidth(initialWidth);\n    }\n\n    // Validate initial padding\n    const paddingValue = parseInt(initialPadding) || 12;\n    if (paddingValue < 0 || paddingValue > 20) {\n      setPaddingError(true);\n      // Set padding to closest valid value\n      setAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\n    } else {\n      setPaddingError(false);\n      setAnnPadding(initialPadding);\n    }\n\n    // Validate initial border radius\n    if (initialBorderRadius < 0 || initialBorderRadius > 20) {\n      setBorderRadiusError(true);\n      // Set border radius to closest valid value\n      setBorderRadius(initialBorderRadius < 0 ? 0 : 20);\n    } else {\n      setBorderRadiusError(false);\n      setBorderRadius(initialBorderRadius);\n    }\n\n    // Validate initial border size\n    if (initialBorderSize < 0 || initialBorderSize > 10) {\n      setBorderSizeError(true);\n      // Set border size to closest valid value\n      setAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\n    } else {\n      setBorderSizeError(false);\n      setAnnBorderSize(initialBorderSize);\n    }\n  }, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\n  const handlePositionClick = positionValue => {\n    setSelectedPosition(positionValue);\n    setBposition(positionValue);\n  };\n  const handleBorderColorChange = e => setBorderColor(e.target.value);\n  const handleBackgroundColorChange = e => setBackgroundColor(e.target.value);\n  const handleBackgroundImageChange = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        const result = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n        setBackgroundImage(result);\n        setImagePreview(result);\n        setIsUnSavedChanges(true);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClearBackgroundImage = () => {\n    setBackgroundImage(\"\");\n    setImagePreview(\"\");\n    setIsUnSavedChanges(true);\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    // Don't apply changes if there's any validation error\n    if (widthError || paddingError || borderRadiusError || borderSizeError) {\n      return;\n    }\n    const canvasData = {\n      Position: selectedPosition,\n      BackgroundColor: backgroundColor,\n      BackgroundImage: backgroundImage,\n      Width: width || 500,\n      Radius: borderRadius !== undefined ? borderRadius : 0,\n      Padding: Annpadding || \"12\",\n      BorderColor: borderColor,\n      BorderSize: AnnborderSize || 0,\n      Zindex: 9999\n    };\n    setCanvasSetting(canvasData);\n\n    // Sync AI announcement canvas settings after applying changes\n    if (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\n      // Use setTimeout to ensure setCanvasSetting completes first\n      setTimeout(() => {\n        syncAIAnnouncementCanvasSettings(canvasData);\n      }, 0);\n    }\n    setBposition(selectedPosition);\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Canvas\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls qadpt-errmsg\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-position-grid\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-ctrl-title\",\n                children: translate(\"Position\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                id: \"pos-container\",\n                container: true,\n                spacing: 1\n                //onClick={handlePositionClick}\n                ,\n                children: positions.map(position => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 4,\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    disableRipple: true\n                    // sx={{\n                    //     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\n                    // }}\n                    ,\n                    onClick: () => handlePositionClick(position.value) // Pass value directly\n                    ,\n                    children: renderPositionIcon(position.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 9\n                  }, this)\n                }, position.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 8\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 6\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${width}`,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate width between 300px and 1200px\n                    if (inputValue < 300 || inputValue > 1200) {\n                      setWidthError(true);\n                    } else {\n                      setWidthError(false);\n                    }\n                    setWidth(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: widthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 6\n            }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 10\n              }, this), translate(\"Value must be between 300px and 1200px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Padding\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${Annpadding}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate padding between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setPaddingError(true);\n                    } else {\n                      setPaddingError(false);\n                    }\n                    setAnnPadding(inputValue.toString());\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: paddingError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 6\n            }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Radius\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${borderRadius}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border radius between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setBorderRadiusError(true);\n                      // Set border radius to closest valid value\n                      // setBorderRadius(inputValue < 0 ? 0 : 20);\n                    } else {\n                      setBorderRadiusError(false);\n                    }\n                    setBorderRadius(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderRadiusError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 6\n            }, this), borderRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Size\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${AnnborderSize}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border size between 0px and 10px\n                    if (inputValue < 0 || inputValue > 10) {\n                      setBorderSizeError(true);\n                    } else {\n                      setBorderSizeError(false);\n                    }\n                    setAnnBorderSize(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderSizeError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 6\n            }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 10px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: borderColor,\n                  onChange: handleBorderColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Background\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: backgroundColor,\n                  onChange: handleBackgroundColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Background Image\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  gap: \"8px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  accept: \"image/*\",\n                  onChange: handleBackgroundImageChange,\n                  style: {\n                    display: \"none\"\n                  },\n                  id: \"background-image-upload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 8\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"background-image-upload\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    component: \"span\",\n                    size: \"small\",\n                    style: {\n                      textTransform: \"none\"\n                    },\n                    children: translate(\"Upload Image\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 8\n                }, this), (backgroundImage || imagePreview) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"8px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: imagePreview || backgroundImage,\n                    alt: \"Background preview\",\n                    style: {\n                      width: \"40px\",\n                      height: \"40px\",\n                      objectFit: \"cover\",\n                      borderRadius: \"4px\",\n                      border: \"1px solid #ddd\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 10\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    onClick: handleClearBackgroundImage,\n                    style: {\n                      textTransform: \"none\",\n                      minWidth: \"auto\"\n                    },\n                    children: translate(\"Clear\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 10\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 6\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: `qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n            disabled: widthError || paddingError || borderRadiusError || borderSizeError,\n            children: translate(\"Apply\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(CanvasSettings, \"2jxim0LZRWY9oHVwyu8H9qoKQns=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = CanvasSettings;\nexport default CanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"CanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "defaultDots", "topLeft", "topRight", "middleLeft", "middleCenter", "middleRight", "bottomLeft", "bottomMiddle", "bottomRight", "topcenter", "warning", "centercenter", "useTranslation", "jsxDEV", "_jsxDEV", "CanvasSettings", "zindeex", "setZindeex", "setShowCanvasSettings", "selectedTemplate", "_s", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "backgroundImage", "setBackgroundImage", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "setIsUnSavedChanges", "currentStep", "syncAIAnnouncementCanvasSettings", "createWithAI", "selectedTemplateTour", "state", "t", "translate", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "widthError", "setWidthError", "paddingError", "setPaddingError", "borderRadiusError", "setBorderRadiusError", "borderSizeError", "setBorderSizeError", "imagePreview", "setImagePreview", "positions", "label", "icon", "dangerouslySetInnerHTML", "__html", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "renderPositionIcon", "positionValue", "isSelected", "_announcementJson$Gui", "_announcementJson$Gui2", "_announcementJson$Gui3", "_announcementJson$Gui4", "_announcementJson$Gui5", "currentStepIndex", "GuideStep", "findIndex", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "toLowerCase", "startsWith", "parseInt", "replace", "String", "canvasData", "<PERSON><PERSON>", "initialWidth", "initialPadding", "initialBorderRadius", "initialBorderSize", "_canvasData$Radius", "Position", "<PERSON><PERSON><PERSON>", "BackgroundColor", "BackgroundImage", "<PERSON><PERSON>", "Padding", "BorderColor", "BorderSize", "paddingValue", "handlePositionClick", "handleBorderColorChange", "e", "target", "handleBackgroundColorChange", "handleBackgroundImageChange", "event", "_event$target$files", "file", "files", "reader", "FileReader", "onload", "_e$target", "result", "readAsDataURL", "handleClearBackgroundImage", "handleClose", "handleApplyChanges", "undefined", "Zindex", "setTimeout", "id", "className", "children", "onClick", "size", "container", "spacing", "map", "position", "item", "xs", "disable<PERSON><PERSON><PERSON>", "variant", "autoFocus", "onChange", "inputValue", "InputProps", "endAdornment", "sx", "border", "error", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "fullWidth", "toString", "type", "flexDirection", "gap", "accept", "htmlFor", "component", "textTransform", "src", "alt", "height", "objectFit", "min<PERSON><PERSON><PERSON>", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/CanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots,topLeft,topCenter,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst CanvasSettings = ({ zindeex, setZindeex, setShowCanvasSettings, selectedTemplate }: any) => {\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundImage,\r\n\t\tsetBackgroundImage,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tcurrentStep,\r\n\t\tsyncAIAnnouncementCanvasSettings,\r\n\t\tcreateWithAI,\r\n\t\t//selectedTemplate,\r\n\t\tselectedTemplateTour\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderRadiusError, setBorderRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst [imagePreview, setImagePreview] = useState<string>(\"\");\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\"), icon: <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\"), icon: <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\"), icon: <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\"), icon: <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />, value: \"left-center\" },\r\n\t\t{ label: translate(\"Middle Center\"), icon: <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />, value: \"center-center\" },\r\n\t\t{ label: translate(\"Middle Right\"), icon: <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />, value: \"right-center\" },\r\n\t\t{ label: translate(\"Bottom Left\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />, value: \"bottom-right\" },\r\n\t];\r\n\tconst renderPositionIcon = (positionValue:any) => {\r\n\t\tconst isSelected = Bposition === positionValue;\r\n\r\n\t\tif (!isSelected) {\r\n\t\t  return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\r\n\t\tswitch (positionValue) {\r\n\t\t  case \"top-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"left-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"center-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"right-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  default:\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\t  };\r\n\r\n\tuseEffect(() => {\r\n\t\tconst currentStepIndex = announcementJson?.GuideStep?.findIndex(\r\n\t\t\t(step: any) => {\r\n\t\t\t\tlet stepNum = step.stepName;// \r\n\t\t\t\tif (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\r\n\t\t\t\t\tstepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\r\n\t\t\t\t}\r\n\t\t\t\treturn String(stepNum) === String(currentStep); \r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Get canvas data for the current step (if found), otherwise use the first step\r\n\t\tconst canvasData = (currentStepIndex !== -1)\r\n\t\t\t? announcementJson?.GuideStep?.[currentStepIndex]?.Canvas\r\n\t\t\t: announcementJson?.GuideStep?.[0]?.Canvas;\r\n\r\n\t\tlet initialWidth;\r\n\t\tlet initialPadding;\r\n\t\tlet initialBorderRadius;\r\n\t\tlet initialBorderSize;\r\n\r\n\t\tif (canvasData) {\r\n\t\t\tsetSelectedPosition(canvasData.Position || \"center-center\");\r\n\t\t\tsetBposition(canvasData.Position || \"center-center\");\r\n\t\t\tinitialWidth = canvasData.Width || 500;\r\n\t\t\tsetBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\r\n\t\t\tsetBackgroundImage(canvasData.BackgroundImage || \"\");\r\n\t\t\tsetImagePreview(canvasData.BackgroundImage || \"\");\r\n\t\t\tinitialBorderRadius = canvasData.Radius ?? 8;\r\n\t\t\tinitialPadding = canvasData.Padding || \"12\";\r\n\t\t\tsetBorderColor(canvasData.BorderColor || \"#000000\");\r\n\t\t\tinitialBorderSize = canvasData.BorderSize || 0;\r\n\t\t} else {\r\n\t\t\tsetSelectedPosition(\"center-center\");\r\n\t\t\tsetBposition(\"center-center\");\r\n\t\t\tinitialWidth = 500;\r\n\t\t\tsetBackgroundColor(\"#ffffff\");\r\n\t\t\tinitialBorderRadius = 8;\r\n\t\t\tinitialPadding = \"12\";\r\n\t\t\tsetBorderColor(\"#000000\");\r\n\t\t\tinitialBorderSize = 0;\r\n\t\t}\r\n\r\n\t\t// Validate initial width\r\n\t\tif (initialWidth < 300 || initialWidth > 1200) {\r\n\t\t\tsetWidthError(true);\r\n\t\t\t// Set width to closest valid value\r\n\t\t\tsetWidth(initialWidth < 300 ? 300 : 1200);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t\tsetWidth(initialWidth);\r\n\t\t}\r\n\r\n\t\t// Validate initial padding\r\n\t\tconst paddingValue = parseInt(initialPadding) || 12;\r\n\t\tif (paddingValue < 0 || paddingValue > 20) {\r\n\t\t\tsetPaddingError(true);\r\n\t\t\t// Set padding to closest valid value\r\n\t\t\tsetAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\r\n\t\t} else {\r\n\t\t\tsetPaddingError(false);\r\n\t\t\tsetAnnPadding(initialPadding);\r\n\t\t}\r\n\r\n\t\t// Validate initial border radius\r\n\t\tif (initialBorderRadius < 0 || initialBorderRadius > 20) {\r\n\t\t\tsetBorderRadiusError(true);\r\n\t\t\t// Set border radius to closest valid value\r\n\t\t\tsetBorderRadius(initialBorderRadius < 0 ? 0 : 20);\r\n\t\t} else {\r\n\t\t\tsetBorderRadiusError(false);\r\n\t\t\tsetBorderRadius(initialBorderRadius);\r\n\t\t}\r\n\r\n\t\t// Validate initial border size\r\n\t\tif (initialBorderSize < 0 || initialBorderSize > 10) {\r\n\t\t\tsetBorderSizeError(true);\r\n\t\t\t// Set border size to closest valid value\r\n\t\t\tsetAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\r\n\t\t} else {\r\n\t\t\tsetBorderSizeError(false);\r\n\t\t\tsetAnnBorderSize(initialBorderSize);\r\n\t\t}\r\n\t}, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\r\n\r\n\tconst handlePositionClick = (positionValue: string) => {\r\n\t\tsetSelectedPosition(positionValue);\r\n\t\tsetBposition(positionValue);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleBackgroundImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onload = (e) => {\r\n\t\t\t\tconst result = e.target?.result as string;\r\n\t\t\t\tsetBackgroundImage(result);\r\n\t\t\t\tsetImagePreview(result);\r\n\t\t\t\tsetIsUnSavedChanges(true);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClearBackgroundImage = () => {\r\n\t\tsetBackgroundImage(\"\");\r\n\t\tsetImagePreview(\"\");\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || borderRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst canvasData = {\r\n\t\t\tPosition: selectedPosition,\r\n\t\t\tBackgroundColor: backgroundColor,\r\n\t\t\tBackgroundImage: backgroundImage,\r\n\t\t\tWidth: width || 500,\r\n\t\t\tRadius: borderRadius !== undefined ? borderRadius : 0,\r\n\t\t\tPadding: Annpadding || \"12\",\r\n\t\t\tBorderColor: borderColor,\r\n\t\t\tBorderSize: AnnborderSize || 0,\r\n\t\t\tZindex: 9999,\r\n\t\t};\r\n\r\n\t\tsetCanvasSetting(canvasData);\r\n\r\n\t\t// Sync AI announcement canvas settings after applying changes\r\n\t\tif (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\r\n\t\t\t// Use setTimeout to ensure setCanvasSetting completes first\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsyncAIAnnouncementCanvasSettings(canvasData);\r\n\t\t\t}, 0);\r\n\t\t}\r\n\r\n\t\tsetBposition(selectedPosition);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t<Grid\r\n\t\t\t\t\t\tid=\"pos-container\"\r\n\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\tspacing={1}\r\n\t\t\t\t\t\t//onClick={handlePositionClick}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Position Icons */}\r\n\t\t\t\t\t\t{positions.map((position) => (\r\n\t\t\t\t\t\t\t<Grid item xs={4} key={position.value}>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tdisableRipple\r\n\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t//     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handlePositionClick(position.value)} // Pass value directly\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{renderPositionIcon(position.value)}\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t{/* Width Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Width\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${width}`}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetWidth(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 300px and 1200px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Height</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${height}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetHeight(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Padding Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${Annpadding}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnPadding(inputValue.toString());\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Border Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Radius\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${borderRadius}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border radius between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t// Set border radius to closest valid value\r\n\t\t\t\t\t\t\t\t\t// setBorderRadius(inputValue < 0 ? 0 : 20);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetBorderRadius(inputValue);\r\n\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\terror={borderRadiusError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${AnnborderSize}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnBorderSize(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 10px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Zindex value Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${zindeex}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetZindeex(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Border Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={borderColor}\r\n\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* Background Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundColor}\r\n\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* Background Image Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background Image\")}</Typography>\r\n\t\t\t\t\t\t<div style={{ display: \"flex\", flexDirection: \"column\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\tonChange={handleBackgroundImageChange}\r\n\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\tid=\"background-image-upload\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<label htmlFor=\"background-image-upload\">\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ textTransform: \"none\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Upload Image\")}\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t{(backgroundImage || imagePreview) && (\r\n\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\tsrc={imagePreview || backgroundImage}\r\n\t\t\t\t\t\t\t\t\t\talt=\"Background preview\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\tobjectFit: \"cover\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ddd\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleClearBackgroundImage}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ textTransform: \"none\", minWidth: \"auto\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Clear\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={widthError || paddingError || borderRadiusError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default CanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAiB,eAAe;AAC7F,OAAOC,SAAS,MAAM,2BAA2B;AAGjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,WAAW,EAACC,OAAO,EAAWC,QAAQ,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,QAAQ,0BAA0B;AAC3L,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,qBAAqB;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACjG,MAAM;IACLC,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,cAAc;IACdC,kBAAkB;IAClBC,eAAe;IACfC,kBAAkB;IAClBC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,mBAAmB;IACnBC,WAAW;IACXC,gCAAgC;IAChCC,YAAY;IACZ;IACAC;EACD,CAAC,GAAG5C,cAAc,CAAE6C,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGlC,cAAc,CAAC,CAAC;EACzC,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAMuE,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEhB,SAAS,CAAC,UAAU,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAEhE;MAAQ,CAAE;MAACiE,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EAC/I;IAAEV,KAAK,EAAEhB,SAAS,CAAC,YAAY,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAExD;MAAU,CAAE;MAACyD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAa,CAAC,EACrJ;IAAEV,KAAK,EAAEhB,SAAS,CAAC,WAAW,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE/D;MAAS,CAAE;MAACgE,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAClJ;IAAEV,KAAK,EAAEhB,SAAS,CAAC,aAAa,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE9D;MAAW,CAAE;MAAC+D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEhB,SAAS,CAAC,eAAe,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE7D;MAAa,CAAE;MAAC8D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEhB,SAAS,CAAC,cAAc,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE5D;MAAY,CAAE;MAAC6D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC3J;IAAEV,KAAK,EAAEhB,SAAS,CAAC,aAAa,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE3D;MAAW,CAAE;MAAC4D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEhB,SAAS,CAAC,eAAe,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAE1D;MAAa,CAAE;MAAC2D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEhB,SAAS,CAAC,cAAc,CAAC;IAAEiB,IAAI,eAAEjD,OAAA;MAAMkD,uBAAuB,EAAE;QAAEC,MAAM,EAAEzD;MAAY,CAAE;MAAC0D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CAC3J;EACD,MAAMC,kBAAkB,GAAIC,aAAiB,IAAK;IACjD,MAAMC,UAAU,GAAGtC,SAAS,KAAKqC,aAAa;IAE9C,IAAI,CAACC,UAAU,EAAE;MACf,oBAAO7D,OAAA;QAAMkD,uBAAuB,EAAE;UAAEC,MAAM,EAAEjE;QAAY,CAAE;QAACkE,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjG;IAEA,QAAQG,aAAa;MACnB,KAAK,UAAU;QAChB,oBAAO5D,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAEhE;UAAQ,CAAE;UAACiE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1F,KAAK,YAAY;QAClB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAExD;UAAU,CAAE;UAACyD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5F,KAAK,WAAW;QACjB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE/D;UAAS,CAAE;UAACgE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,aAAa;QACnB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE9D;UAAW,CAAE;UAAC+D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE7D;UAAa,CAAE;UAAC8D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE5D;UAAY,CAAE;UAAC6D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F,KAAK,aAAa;QACnB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE3D;UAAW,CAAE;UAAC4D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAE1D;UAAa,CAAE;UAAC2D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAEzD;UAAY,CAAE;UAAC0D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F;QACD,oBAAOzD,OAAA;UAAMkD,uBAAuB,EAAE;YAAEC,MAAM,EAAEjE;UAAY,CAAE;UAACkE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAChG;EACC,CAAC;EAEHlF,SAAS,CAAC,MAAM;IAAA,IAAAuF,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACf,MAAMC,gBAAgB,GAAG1D,gBAAgB,aAAhBA,gBAAgB,wBAAAqD,qBAAA,GAAhBrD,gBAAgB,CAAE2D,SAAS,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,SAAS,CAC7DC,IAAS,IAAK;MACd,IAAIC,OAAO,GAAGD,IAAI,CAACE,QAAQ,CAAC;MAC5B,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACE,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC5EH,OAAO,GAAGI,QAAQ,CAACJ,OAAO,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACvD;MACA,OAAOC,MAAM,CAACN,OAAO,CAAC,KAAKM,MAAM,CAACnD,WAAW,CAAC;IAC/C,CACD,CAAC;;IAED;IACA,MAAMoD,UAAU,GAAIX,gBAAgB,KAAK,CAAC,CAAC,GACxC1D,gBAAgB,aAAhBA,gBAAgB,wBAAAsD,sBAAA,GAAhBtD,gBAAgB,CAAE2D,SAAS,cAAAL,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8BI,gBAAgB,CAAC,cAAAH,sBAAA,uBAA/CA,sBAAA,CAAiDe,MAAM,GACvDtE,gBAAgB,aAAhBA,gBAAgB,wBAAAwD,sBAAA,GAAhBxD,gBAAgB,CAAE2D,SAAS,cAAAH,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8B,CAAC,CAAC,cAAAC,sBAAA,uBAAhCA,sBAAA,CAAkCa,MAAM;IAE3C,IAAIC,YAAY;IAChB,IAAIC,cAAc;IAClB,IAAIC,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIL,UAAU,EAAE;MAAA,IAAAM,kBAAA;MACfhD,mBAAmB,CAAC0C,UAAU,CAACO,QAAQ,IAAI,eAAe,CAAC;MAC3D7D,YAAY,CAACsD,UAAU,CAACO,QAAQ,IAAI,eAAe,CAAC;MACpDL,YAAY,GAAGF,UAAU,CAACQ,KAAK,IAAI,GAAG;MACtCxE,kBAAkB,CAACgE,UAAU,CAACS,eAAe,IAAI,SAAS,CAAC;MAC3DvE,kBAAkB,CAAC8D,UAAU,CAACU,eAAe,IAAI,EAAE,CAAC;MACpD1C,eAAe,CAACgC,UAAU,CAACU,eAAe,IAAI,EAAE,CAAC;MACjDN,mBAAmB,IAAAE,kBAAA,GAAGN,UAAU,CAACW,MAAM,cAAAL,kBAAA,cAAAA,kBAAA,GAAI,CAAC;MAC5CH,cAAc,GAAGH,UAAU,CAACY,OAAO,IAAI,IAAI;MAC3C7E,cAAc,CAACiE,UAAU,CAACa,WAAW,IAAI,SAAS,CAAC;MACnDR,iBAAiB,GAAGL,UAAU,CAACc,UAAU,IAAI,CAAC;IAC/C,CAAC,MAAM;MACNxD,mBAAmB,CAAC,eAAe,CAAC;MACpCZ,YAAY,CAAC,eAAe,CAAC;MAC7BwD,YAAY,GAAG,GAAG;MAClBlE,kBAAkB,CAAC,SAAS,CAAC;MAC7BoE,mBAAmB,GAAG,CAAC;MACvBD,cAAc,GAAG,IAAI;MACrBpE,cAAc,CAAC,SAAS,CAAC;MACzBsE,iBAAiB,GAAG,CAAC;IACtB;;IAEA;IACA,IAAIH,YAAY,GAAG,GAAG,IAAIA,YAAY,GAAG,IAAI,EAAE;MAC9C1C,aAAa,CAAC,IAAI,CAAC;MACnB;MACA3B,QAAQ,CAACqE,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;IAC1C,CAAC,MAAM;MACN1C,aAAa,CAAC,KAAK,CAAC;MACpB3B,QAAQ,CAACqE,YAAY,CAAC;IACvB;;IAEA;IACA,MAAMa,YAAY,GAAGlB,QAAQ,CAACM,cAAc,CAAC,IAAI,EAAE;IACnD,IAAIY,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MAC1CrD,eAAe,CAAC,IAAI,CAAC;MACrB;MACApB,aAAa,CAACyE,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7C,CAAC,MAAM;MACNrD,eAAe,CAAC,KAAK,CAAC;MACtBpB,aAAa,CAAC6D,cAAc,CAAC;IAC9B;;IAEA;IACA,IAAIC,mBAAmB,GAAG,CAAC,IAAIA,mBAAmB,GAAG,EAAE,EAAE;MACxDxC,oBAAoB,CAAC,IAAI,CAAC;MAC1B;MACAxB,eAAe,CAACgE,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC,MAAM;MACNxC,oBAAoB,CAAC,KAAK,CAAC;MAC3BxB,eAAe,CAACgE,mBAAmB,CAAC;IACrC;;IAEA;IACA,IAAIC,iBAAiB,GAAG,CAAC,IAAIA,iBAAiB,GAAG,EAAE,EAAE;MACpDvC,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAtB,gBAAgB,CAAC6D,iBAAiB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC,MAAM;MACNvC,kBAAkB,CAAC,KAAK,CAAC;MACzBtB,gBAAgB,CAAC6D,iBAAiB,CAAC;IACpC;EACD,CAAC,EAAE,CAAC1E,gBAAgB,EAAEiB,WAAW,EAAEF,YAAY,EAAEb,QAAQ,EAAEG,kBAAkB,EAAEI,eAAe,EAAEE,aAAa,EAAEP,cAAc,EAAES,gBAAgB,CAAC,CAAC;EAEjJ,MAAMwE,mBAAmB,GAAIlC,aAAqB,IAAK;IACtDxB,mBAAmB,CAACwB,aAAa,CAAC;IAClCpC,YAAY,CAACoC,aAAa,CAAC;EAC5B,CAAC;EACD,MAAMmC,uBAAuB,GAAIC,CAAM,IAAKnF,cAAc,CAACmF,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;EAC1E,MAAMwC,2BAA2B,GAAIF,CAAM,IAAKlF,kBAAkB,CAACkF,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC;EAElF,MAAMyC,2BAA2B,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACnF,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACH,MAAM,CAACM,KAAK,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACT,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIV,CAAC,IAAK;QAAA,IAAAW,SAAA;QACtB,MAAMC,MAAM,IAAAD,SAAA,GAAGX,CAAC,CAACC,MAAM,cAAAU,SAAA,uBAARA,SAAA,CAAUC,MAAgB;QACzC5F,kBAAkB,CAAC4F,MAAM,CAAC;QAC1B9D,eAAe,CAAC8D,MAAM,CAAC;QACvBnF,mBAAmB,CAAC,IAAI,CAAC;MAC1B,CAAC;MACD+E,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMQ,0BAA0B,GAAGA,CAAA,KAAM;IACxC9F,kBAAkB,CAAC,EAAE,CAAC;IACtB8B,eAAe,CAAC,EAAE,CAAC;IACnBrB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMsF,WAAW,GAAGA,CAAA,KAAM;IACzB7E,SAAS,CAAC,KAAK,CAAC;IAChB9B,qBAAqB,CAAC,KAAK,CAAC;EAC7B,CAAC;EACD,MAAM4G,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,IAAI3E,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,EAAE;MACvE;IACD;IAEA,MAAMmC,UAAU,GAAG;MAClBO,QAAQ,EAAElD,gBAAgB;MAC1BoD,eAAe,EAAE3E,eAAe;MAChC4E,eAAe,EAAEzE,eAAe;MAChCuE,KAAK,EAAE5E,KAAK,IAAI,GAAG;MACnB+E,MAAM,EAAExE,YAAY,KAAKgG,SAAS,GAAGhG,YAAY,GAAG,CAAC;MACrDyE,OAAO,EAAEvE,UAAU,IAAI,IAAI;MAC3BwE,WAAW,EAAEnF,WAAW;MACxBoF,UAAU,EAAEvE,aAAa,IAAI,CAAC;MAC9B6F,MAAM,EAAE;IACT,CAAC;IAED3G,gBAAgB,CAACuE,UAAU,CAAC;;IAE5B;IACA,IAAIlD,YAAY,KAAKvB,gBAAgB,KAAK,cAAc,IAAIwB,oBAAoB,KAAK,cAAc,CAAC,EAAE;MACrG;MACAsF,UAAU,CAAC,MAAM;QAChBxF,gCAAgC,CAACmD,UAAU,CAAC;MAC7C,CAAC,EAAE,CAAC,CAAC;IACN;IAEAtD,YAAY,CAACW,gBAAgB,CAAC;IAC9B4E,WAAW,CAAC,CAAC;IACbtF,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAI,CAACQ,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACAjC,OAAA;MACCoH,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7BtH,OAAA;QAAKqH,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7BtH,OAAA;UAAKqH,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnCtH,OAAA,CAACnB,UAAU;YACV,cAAW,OAAO;YAClB0I,OAAO,EAAER,WAAY;YAAAO,QAAA,eAErBtH,OAAA,CAAChB,2BAA2B;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEpB,CAAC,eACbzD,OAAA;YAAKqH,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEtF,SAAS,CAAC,QAAQ;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExDzD,OAAA,CAACnB,UAAU;YACV2I,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBD,OAAO,EAAER,WAAY;YAAAO,QAAA,eAErBtH,OAAA,CAACjB,SAAS;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNzD,OAAA;UAAKqH,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC/BtH,OAAA;YAAKqH,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC5CtH,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBACjCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,UAAU;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EzD,OAAA,CAACpB,IAAI;gBACJwI,EAAE,EAAC,eAAe;gBAClBK,SAAS;gBACTC,OAAO,EAAE;gBACT;gBAAA;gBAAAJ,QAAA,EAGCvE,SAAS,CAAC4E,GAAG,CAAEC,QAAQ,iBACvB5H,OAAA,CAACpB,IAAI;kBAACiJ,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAR,QAAA,eAChBtH,OAAA,CAACnB,UAAU;oBACV2I,IAAI,EAAC,OAAO;oBACZO,aAAa;oBACb;oBACA;oBACA;oBAAA;oBACAR,OAAO,EAAEA,CAAA,KAAMzB,mBAAmB,CAAC8B,QAAQ,CAAClE,KAAK,CAAE,CAAC;oBAAA;oBAAA4D,QAAA,EAEnD3D,kBAAkB,CAACiE,QAAQ,CAAClE,KAAK;kBAAC;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC,GAVSmE,QAAQ,CAAClE,KAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW/B,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAILzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA;gBAAKqH,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,OAAO;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA,CAACrB,SAAS;kBACTqJ,OAAO,EAAC,UAAU;kBAClBtE,KAAK,EAAE,GAAGhD,KAAK,EAAG;kBAClB8G,IAAI,EAAC,OAAO;kBACZS,SAAS;kBACTZ,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGlC,CAAC,IAAK;oBAChB,MAAMmC,UAAU,GAAGxD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIyE,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,IAAI,EAAE;sBAC1C7F,aAAa,CAAC,IAAI,CAAC;oBACpB,CAAC,MAAM;sBACNA,aAAa,CAAC,KAAK,CAAC;oBACrB;oBAEA3B,QAAQ,CAACwH,UAAU,CAAC;kBACrB,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAEnG;gBAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF,CAAC,EACLpB,UAAU,iBACXrC,OAAA,CAACtB,UAAU;cACV0E,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBoF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAClJ;cACZ,CAAE;cAAAyH,QAAA,gBACAtH,OAAA;gBAAMoD,KAAK,EAAE;kBAAE0F,OAAO,EAAE,MAAM;kBAAEzF,QAAQ,EAAE,MAAM;kBAAE0F,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F9F,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvD;gBAAQ;cAAE;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACCzB,SAAS,CAAC,yCAAyC,CAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACZ,eAuBDzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,SAAS;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA,CAACrB,SAAS;kBACTqJ,OAAO,EAAC,UAAU;kBAClBtE,KAAK,EAAE,GAAGvC,UAAU,EAAG;kBACvB8H,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGlC,CAAC,IAAK;oBAChB,MAAMmC,UAAU,GAAGxD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIyE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtC3F,eAAe,CAAC,IAAI,CAAC;oBACtB,CAAC,MAAM;sBACNA,eAAe,CAAC,KAAK,CAAC;oBACvB;oBAEApB,aAAa,CAAC+G,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC;kBACrC,CAAE;kBACFd,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAEjG;gBAAa;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlB,YAAY,iBACZvC,OAAA,CAACtB,UAAU;cACX0E,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBoF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAClJ;cACZ,CAAE;cAAAyH,QAAA,gBACAtH,OAAA;gBAAMoD,KAAK,EAAE;kBAAE0F,OAAO,EAAE,MAAM;kBAAEzF,QAAQ,EAAE,MAAM;kBAAE0F,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F9F,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvD;gBAAQ;cAAE;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEzB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAGDzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,eAAe;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrFzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA,CAACrB,SAAS;kBACTqJ,OAAO,EAAC,UAAU;kBAClBtE,KAAK,EAAE,GAAGzC,YAAY,EAAG;kBACzBgI,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGlC,CAAC,IAAK;oBAChB,MAAMmC,UAAU,GAAGxD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIyE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCzF,oBAAoB,CAAC,IAAI,CAAC;sBAC1B;sBACA;oBACD,CAAC,MAAM;sBACNA,oBAAoB,CAAC,KAAK,CAAC;oBAC5B;oBACCxB,eAAe,CAACiH,UAAU,CAAC;kBAE7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACC,CAAE;kBACFC,KAAK,EAAE/F;gBAAkB;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACLhB,iBAAiB,iBACjBzC,OAAA,CAACtB,UAAU;cACX0E,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBoF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAClJ;cACZ,CAAE;cAAAyH,QAAA,gBACAtH,OAAA;gBAAMoD,KAAK,EAAE;kBAAE0F,OAAO,EAAE,MAAM;kBAAEzF,QAAQ,EAAE,MAAM;kBAAE0F,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F9F,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvD;gBAAQ;cAAE;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEzB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eACDzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,aAAa;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnFzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA,CAACrB,SAAS;kBACTqJ,OAAO,EAAC,UAAU;kBAClBtE,KAAK,EAAE,GAAGrC,aAAa,EAAG;kBAC1B4H,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGlC,CAAC,IAAK;oBAChB,MAAMmC,UAAU,GAAGxD,QAAQ,CAACqB,CAAC,CAACC,MAAM,CAACvC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAIyE,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCvF,kBAAkB,CAAC,IAAI,CAAC;oBACzB,CAAC,MAAM;sBACNA,kBAAkB,CAAC,KAAK,CAAC;oBAC1B;oBAEAtB,gBAAgB,CAAC6G,UAAU,CAAC;kBAC7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE7F;gBAAgB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLd,eAAe,iBACf3C,OAAA,CAACtB,UAAU;cACX0E,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBoF,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAClJ;cACZ,CAAE;cAAAyH,QAAA,gBACAtH,OAAA;gBAAMoD,KAAK,EAAE;kBAAE0F,OAAO,EAAE,MAAM;kBAAEzF,QAAQ,EAAE,MAAM;kBAAE0F,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9F9F,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvD;gBAAQ;cAAE;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEzB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAkBDzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,QAAQ;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9EzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA;kBACCmJ,IAAI,EAAC,OAAO;kBACZzF,KAAK,EAAElD,WAAY;kBACnB0H,QAAQ,EAAEnC,uBAAwB;kBAClCsB,SAAS,EAAC;gBAAmB;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,YAAY;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClFzD,OAAA;gBAAAsH,QAAA,eACDtH,OAAA;kBACCmJ,IAAI,EAAC,OAAO;kBACZzF,KAAK,EAAE9C,eAAgB;kBACvBsH,QAAQ,EAAEhC,2BAA4B;kBACtCmB,SAAS,EAAC;gBAAmB;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNzD,OAAA,CAACvB,GAAG;cAAC4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjCtH,OAAA,CAACtB,UAAU;gBAAC2I,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtF,SAAS,CAAC,kBAAkB;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxFzD,OAAA;gBAAKoD,KAAK,EAAE;kBAAE0F,OAAO,EAAE,MAAM;kBAAEM,aAAa,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAM,CAAE;gBAAA/B,QAAA,gBACpEtH,OAAA;kBACCmJ,IAAI,EAAC,MAAM;kBACXG,MAAM,EAAC,SAAS;kBAChBpB,QAAQ,EAAE/B,2BAA4B;kBACtC/C,KAAK,EAAE;oBAAE0F,OAAO,EAAE;kBAAO,CAAE;kBAC3B1B,EAAE,EAAC;gBAAyB;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACFzD,OAAA;kBAAOuJ,OAAO,EAAC,yBAAyB;kBAAAjC,QAAA,eACvCtH,OAAA,CAAClB,MAAM;oBACNkJ,OAAO,EAAC,UAAU;oBAClBwB,SAAS,EAAC,MAAM;oBAChBhC,IAAI,EAAC,OAAO;oBACZpE,KAAK,EAAE;sBAAEqG,aAAa,EAAE;oBAAO,CAAE;oBAAAnC,QAAA,EAEhCtF,SAAS,CAAC,cAAc;kBAAC;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACP,CAAC1C,eAAe,IAAI8B,YAAY,kBAChC7C,OAAA;kBAAKoD,KAAK,EAAE;oBAAE0F,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEM,GAAG,EAAE;kBAAM,CAAE;kBAAA/B,QAAA,gBACjEtH,OAAA;oBACC0J,GAAG,EAAE7G,YAAY,IAAI9B,eAAgB;oBACrC4I,GAAG,EAAC,oBAAoB;oBACxBvG,KAAK,EAAE;sBACN1C,KAAK,EAAE,MAAM;sBACbkJ,MAAM,EAAE,MAAM;sBACdC,SAAS,EAAE,OAAO;sBAClB5I,YAAY,EAAE,KAAK;sBACnBsH,MAAM,EAAE;oBACT;kBAAE;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFzD,OAAA,CAAClB,MAAM;oBACNkJ,OAAO,EAAC,UAAU;oBAClBR,IAAI,EAAC,OAAO;oBACZD,OAAO,EAAET,0BAA2B;oBACpC1D,KAAK,EAAE;sBAAEqG,aAAa,EAAE,MAAM;sBAAEK,QAAQ,EAAE;oBAAO,CAAE;oBAAAxC,QAAA,EAElDtF,SAAS,CAAC,OAAO;kBAAC;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CACL;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPzD,OAAA;UAAKqH,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCtH,OAAA,CAAClB,MAAM;YACNkJ,OAAO,EAAC,WAAW;YACnBT,OAAO,EAAEP,kBAAmB;YAC5BK,SAAS,EAAE,aAAahF,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/GoH,QAAQ,EAAE1H,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB;YAAA2E,QAAA,EAE7EtF,SAAS,CAAC,OAAO;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF;IACL;EAAA;AAEF,CAAC;AAACnD,EAAA,CA/nBIL,cAAc;EAAA,QA0BfhB,cAAc,EACOa,cAAc;AAAA;AAAAkK,EAAA,GA3BlC/J,cAAc;AAioBpB,eAAeA,cAAc;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\Tooltips\\\\designFields\\\\TooltipCanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { AlignHorizontalLeft as TopLeftIcon, AlignHorizontalCenter as TopCenterIcon, AlignHorizontalRight as TopRightIcon, AlignVerticalTop as MiddleLeftIcon, AlignVerticalCenter as MiddleCenterIcon, AlignVerticalBottom as MiddleRightIcon, AlignHorizontalLeft as BottomLeftIcon, AlignHorizontalCenter as BottomCenterIcon, AlignHorizontalRight as BottomRightIcon } from \"@mui/icons-material\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE } from \"../../../store/drawerStore\";\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TooltipCanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowTooltipCanvasSettings\n}) => {\n  _s();\n  var _toolTipGuideMetaData2;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setTooltipXaxis,\n    setTooltipYaxis,\n    updateCanvasInTooltip,\n    tooltipXaxis,\n    tooltipYaxis,\n    tooltipWidth,\n    setTooltipWidth,\n    setTooltipPadding,\n    setTooltipBorderradius,\n    setTooltipBordersize,\n    setTooltipBordercolor,\n    setTooltipBackgroundcolor,\n    tooltipBackgroundImage,\n    setTooltipBackgroundImage,\n    tooltippadding,\n    tooltipborderradius,\n    tooltipbordersize,\n    tooltipBordercolor,\n    tooltipBackgroundcolor,\n    tooltipPosition,\n    setTooltipPosition,\n    setElementSelected,\n    setIsTooltipPopup,\n    toolTipGuideMetaData,\n    currentStep,\n    autoPosition,\n    setAutoPosition,\n    selectedTemplate,\n    updateDesignelementInTooltip,\n    selectedTemplateTour,\n    CANVAS_DEFAULT_VALUE_HOTSPOT,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(true);\n  const [dismiss, setDismiss] = useState(false);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [error, setError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [cornerRadiusError, setCornerRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\n  const [imagePreview, setImagePreview] = useState(tooltipBackgroundImage);\n  useEffect(() => {\n    // Sync tempBorderColor with store, using default if empty or transparent\n    const validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\n    setTempBorderColor(validColor);\n  }, [tooltipBordercolor]);\n  const positions = [{\n    label: translate(\"Top Left\", {\n      defaultValue: \"Top Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 71\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\", {\n      defaultValue: \"Top Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipTop\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 146\n    }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 236\n    }, this),\n    value: \"top\"\n  }, {\n    label: translate(\"Top Right\", {\n      defaultValue: \"Top Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(TopRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 73\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\", {\n      defaultValue: \"Middle Left\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 148\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 239\n    }, this),\n    value: \"left\"\n  }, {\n    label: translate(\"Middle Center\", {\n      defaultValue: \"Middle Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 245\n    }, this),\n    value: \"center\"\n  }, {\n    label: translate(\"Middle Right\", {\n      defaultValue: \"Middle Right\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 150\n    }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 242\n    }, this),\n    value: \"right\"\n  }, {\n    label: translate(\"Bottom Left\", {\n      defaultValue: \"Bottom Left\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomLeftIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 77\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\", {\n      defaultValue: \"Bottom Center\"\n    }),\n    icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: tooltipBottom\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 152\n    }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 245\n    }, this),\n    value: \"bottom\"\n  }, {\n    label: translate(\"Bottom Right\", {\n      defaultValue: \"Bottom Right\"\n    }),\n    icon: /*#__PURE__*/_jsxDEV(BottomRightIcon, {\n      fontSize: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 79\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const handlePositionClick = e => {\n    var _e$target;\n    if (e !== null && e !== void 0 && (_e$target = e.target) !== null && _e$target !== void 0 && _e$target.id) {\n      //setSelectedPosition(e.target.id);\n      setTooltipPosition(e.target.id);\n    }\n  };\n  const onReselectElement = () => {\n    TooltipCanvasSettings({\n      ReSelection: false,\n      XPosition: 4,\n      YPosition: 4,\n      width: \"300\",\n      Padding: \"2\",\n      borderradius: \"8\",\n      bordersize: \"0\",\n      borderColor: \"\",\n      backgroundColor: \"\",\n      // PulseAnimation: true,\n      // stopAnimationUponInteraction: true,\n      // ShowUpon: \"Hovering Hotspot\",\n      ShowByDefault: false\n    });\n    updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    setElementSelected(true);\n    setIsTooltipPopup(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleBorderColorChange = e => {\n    var _e$target2;\n    if (e !== null && e !== void 0 && (_e$target2 = e.target) !== null && _e$target2 !== void 0 && _e$target2.value) {\n      setTempBorderColor(e.target.value);\n    }\n  };\n  const handleBackgroundColorChange = e => {\n    var _e$target3;\n    if (e !== null && e !== void 0 && (_e$target3 = e.target) !== null && _e$target3 !== void 0 && _e$target3.value) {\n      setTooltipBackgroundcolor(e.target.value);\n    }\n  };\n  const handleBackgroundImageChange = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target4;\n        const result = (_e$target4 = e.target) === null || _e$target4 === void 0 ? void 0 : _e$target4.result;\n        setTooltipBackgroundImage(result);\n        setImagePreview(result);\n        setIsUnSavedChanges(true);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClearBackgroundImage = () => {\n    setTooltipBackgroundImage(\"\");\n    setImagePreview(\"\");\n    setIsUnSavedChanges(true);\n  };\n  const handleAutoSelect = e => {\n    //\tsetDismiss(e.target.checked);\n    setAutoPosition(e.target.checked);\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowTooltipCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    // Create the new canvas settings\n    const updatedCanvasSettings = {\n      position: tooltipPosition,\n      backgroundColor: tooltipBackgroundcolor,\n      backgroundImage: tooltipBackgroundImage,\n      width: tooltipWidth,\n      borderRadius: tooltipborderradius,\n      padding: tooltippadding,\n      borderColor: tempBorderColor,\n      borderSize: tooltipbordersize,\n      autoposition: autoPosition,\n      xaxis: tooltipXaxis,\n      yaxis: tooltipYaxis\n    };\n\n    // Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\n    updateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  useEffect(() => {\n    var _toolTipGuideMetaData;\n    if ((_toolTipGuideMetaData = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData !== void 0 && _toolTipGuideMetaData.canvas) {\n      const canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\n\n      // Handle border color - use default if empty, transparent, or invalid\n      const borderColor = canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderColor;\n      const validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\n      setTooltipPosition((canvasData === null || canvasData === void 0 ? void 0 : canvasData.position) || \"middle-center\");\n      setTooltipPadding((canvasData === null || canvasData === void 0 ? void 0 : canvasData.padding) || \"10px\");\n      setTooltipBorderradius((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderRadius) || \"8px\");\n      setTooltipBordersize((canvasData === null || canvasData === void 0 ? void 0 : canvasData.borderSize) || \"0px\");\n      setTooltipBordercolor(borderColor || \"\");\n      setTempBorderColor(validBorderColor); // Use valid color for the input\n      setTooltipBackgroundcolor((canvasData === null || canvasData === void 0 ? void 0 : canvasData.backgroundColor) || \"#FFFFFF\");\n      setTooltipWidth((canvasData === null || canvasData === void 0 ? void 0 : canvasData.width) || \"300px\");\n      if (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"2px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"2px\");\n      } else {\n        setTooltipXaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.xaxis) || \"100px\");\n        setTooltipYaxis((canvasData === null || canvasData === void 0 ? void 0 : canvasData.yaxis) || \"100px\");\n      }\n    }\n  }, [(_toolTipGuideMetaData2 = toolTipGuideMetaData[currentStep - 1]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.canvas]);\n  if (!isOpen) return null;\n  const formatValueWithPixelOrPercentage = value => {\n    const v = String(value);\n    let newValue = v;\n    if (v !== null && v !== void 0 && v.endsWith(\"px\") || v !== null && v !== void 0 && v.endsWith(\"%\")) {\n      newValue = v.split(/px|%/)[0];\n    }\n    return newValue;\n  };\n  const handleChange = e => {\n    // Only allow numeric input\n    const value = e.target.value;\n    if (!/^-?\\d*$/.test(value)) {\n      return;\n    }\n    let inputValue = parseInt(value) || 0;\n    setTooltipWidth(`${inputValue}px`);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? translate(\"Canvas\") : translate(\"Canvas\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Auto Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: autoPosition,\n                  onChange: handleAutoSelect,\n                  name: \"autoPosition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-position-grid\",\n            sx: {\n              opacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? 0.5 : 1,\n              cursor: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? \"not-allowed\" : \"\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-ctrl-title\",\n              children: translate(\"Position\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[0].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[1].value,\n                  onClick: () => {\n                    //setSelectedPosition(positions[1].value);\n                    setTooltipPosition(positions[1].value);\n                  },\n                  disabled: autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\",\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[1].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipTop\n                    },\n                    id: positions[1].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(TopCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[2].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[3].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[3].value);\n                    setTooltipPosition(positions[3].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[3].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipLeft\n                    },\n                    id: positions[3].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleLeftIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[4].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[4].value);\n                    setTooltipPosition(positions[4].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\n                    paddingLeft: \"0 !important\"\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipCenter\n                    },\n                    id: positions[4].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[5].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[5].value);\n                    setTooltipPosition(positions[5].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[5].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipRight\n                    },\n                    id: positions[5].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(MiddleRightIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[6].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  id: positions[7].value,\n                  onClick: () => {\n                    // setSelectedPosition(positions[7].value);\n                    setTooltipPosition(positions[7].value);\n                  },\n                  disabled: autoPosition,\n                  disableRipple: true,\n                  sx: {\n                    opacity: tooltipPosition === positions[7].value ? 1 : 0.5\n                  },\n                  children: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: tooltipBottom\n                    },\n                    id: positions[7].value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 9\n                  }, this) : /*#__PURE__*/_jsxDEV(BottomCenterIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 9\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 4,\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  disabled: autoPosition,\n                  sx: {\n                    opacity: selectedPosition === positions[8].value ? 1 : 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 5\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 3\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"X \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipXaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: [\"Y \", translate(\"Axis Offset\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipYaxis),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`),\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                disabled: autoPosition\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            style: {\n              flexDirection: \"column\",\n              height: \"auto\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: \"8px\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: formatValueWithPixelOrPercentage(tooltipWidth),\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: handleChange,\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Padding\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltippadding),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate padding between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setPaddingError(true);\n                  } else {\n                    setPaddingError(false);\n                  }\n                  setTooltipPadding(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: paddingError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 7\n          }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Corner Radius\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipborderradius),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate corner radius between 0px and 20px\n                  if (inputValue < 0 || inputValue > 20) {\n                    setCornerRadiusError(true);\n                  } else {\n                    setCornerRadiusError(false);\n                  }\n                  setTooltipBorderradius(`${inputValue}`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: cornerRadiusError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 7\n          }, this), cornerRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 9\n            }, this), translate(\"Value must be between 0px and 20px.\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border Size\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                value: formatValueWithPixelOrPercentage(tooltipbordersize),\n                size: \"small\",\n                className: \"qadpt-control-input\",\n                onChange: e => {\n                  // Only allow numeric input\n                  const value = e.target.value;\n                  if (!/^-?\\d*$/.test(value)) {\n                    return;\n                  }\n                  const inputValue = parseInt(value) || 0;\n                  // Validate border size between 0px and 20px\n                  if (inputValue < 0 || inputValue > 5) {\n                    setBorderSizeError(true);\n                  } else {\n                    setBorderSizeError(false);\n                  }\n                  setTooltipBordersize(`${inputValue}px`);\n                },\n                InputProps: {\n                  endAdornment: \"px\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    }\n                  }\n                },\n                error: borderSizeError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 1\n          }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 5\n            }, this), \"Value must be between 0px and 5px.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Border\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tempBorderColor || \"#000000\",\n                onChange: handleBorderColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Background\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: tooltipBackgroundcolor,\n                onChange: handleBackgroundColorChange,\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 811,\n                columnNumber: 8\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 810,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Background Image\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: \"8px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \"image/*\",\n                onChange: handleBackgroundImageChange,\n                style: {\n                  display: \"none\"\n                },\n                id: \"tooltip-background-image-upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"tooltip-background-image-upload\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  component: \"span\",\n                  size: \"small\",\n                  style: {\n                    textTransform: \"none\"\n                  },\n                  children: translate(\"Upload Image\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 9\n              }, this), (tooltipBackgroundImage || imagePreview) && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: \"8px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: imagePreview || tooltipBackgroundImage,\n                  alt: \"Background preview\",\n                  style: {\n                    width: \"40px\",\n                    height: \"40px\",\n                    objectFit: \"cover\",\n                    borderRadius: \"4px\",\n                    border: \"1px solid #ddd\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  onClick: handleClearBackgroundImage,\n                  style: {\n                    textTransform: \"none\",\n                    minWidth: \"auto\"\n                  },\n                  children: translate(\"Clear\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n          disabled: paddingError || cornerRadiusError || borderSizeError // Disable button if any validation errors exist\n          ,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 3\n  }, this);\n};\n_s(TooltipCanvasSettings, \"FjcMfEidLU4O3d4lCL8imaJIqGY=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = TooltipCanvasSettings;\nexport default TooltipCanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"TooltipCanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "AlignHorizontalLeft", "TopLeftIcon", "AlignHorizontalCenter", "TopCenterIcon", "AlignHorizontalRight", "TopRightIcon", "AlignVerticalTop", "MiddleLeftIcon", "AlignVerticalCenter", "MiddleCenterIcon", "AlignVerticalBottom", "MiddleRightIcon", "BottomLeftIcon", "BottomCenterIcon", "BottomRightIcon", "useDrawerStore", "CANVAS_DEFAULT_VALUE", "tooltipLeft", "tooltipRight", "tooltipTop", "tooltipBottom", "tooltipCenter", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "TooltipCanvasSettings", "zindeex", "setZindeex", "setShowTooltipCanvasSettings", "_s", "_toolTipGuideMetaData2", "t", "translate", "setTooltipXaxis", "setTooltipYaxis", "updateCanvasInTooltip", "tooltipXaxis", "tooltipYaxis", "tooltipWidth", "setTooltipWidth", "setTooltipPadding", "setTooltipBorderradius", "setTooltipBordersize", "setTooltipBordercolor", "setTooltipBackgroundcolor", "tooltipBackgroundImage", "setTooltipBackgroundImage", "tooltippadding", "tooltipborderradius", "tooltipbordersize", "tooltipBordercolor", "tooltipBackgroundcolor", "tooltipPosition", "setTooltipPosition", "setElementSelected", "setIsTooltipPopup", "toolTipGuideMetaData", "currentStep", "autoPosition", "setAutoPosition", "selectedTemplate", "updateDesignelementInTooltip", "selectedTemplateTour", "CANVAS_DEFAULT_VALUE_HOTSPOT", "setIsUnSavedChanges", "state", "isOpen", "setIsOpen", "dismiss", "<PERSON><PERSON><PERSON><PERSON>", "selectedPosition", "setSelectedPosition", "error", "setError", "paddingError", "setPaddingError", "cornerRadiusError", "setCornerRadiusError", "borderSizeError", "setBorderSizeError", "tempBorderColor", "setTempBorderColor", "imagePreview", "setImagePreview", "validColor", "positions", "label", "defaultValue", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "dangerouslySetInnerHTML", "__html", "style", "handlePositionClick", "e", "_e$target", "target", "id", "onReselectElement", "ReSelection", "XPosition", "YPosition", "width", "Padding", "<PERSON><PERSON><PERSON>", "bordersize", "borderColor", "backgroundColor", "ShowByDefault", "handleBorderColorChange", "_e$target2", "handleBackgroundColorChange", "_e$target3", "handleBackgroundImageChange", "event", "_event$target$files", "file", "files", "reader", "FileReader", "onload", "_e$target4", "result", "readAsDataURL", "handleClearBackgroundImage", "handleAutoSelect", "checked", "handleClose", "handleApplyChanges", "updatedCanvasSettings", "position", "backgroundImage", "borderRadius", "padding", "borderSize", "autoposition", "xaxis", "yaxis", "_toolTipGuideMetaData", "canvas", "canvasData", "validBorderColor", "formatValueWithPixelOrPercentage", "v", "String", "newValue", "endsWith", "split", "handleChange", "test", "inputValue", "parseInt", "className", "children", "onClick", "size", "type", "onChange", "name", "sx", "opacity", "cursor", "container", "spacing", "item", "xs", "disabled", "disable<PERSON><PERSON><PERSON>", "paddingLeft", "variant", "InputProps", "endAdornment", "border", "flexDirection", "height", "display", "alignItems", "gap", "color", "textAlign", "top", "left", "marginBottom", "marginRight", "accept", "htmlFor", "component", "textTransform", "src", "alt", "objectFit", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/Tooltips/designFields/TooltipCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, TextField, Grid, IconButton, Button, Tooltip, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport {\r\n\tAlignHorizontalLeft as TopLeftIcon,\r\n\tAlignHorizontalCenter as TopCenterIcon,\r\n\tAlignHorizontalRight as TopRightIcon,\r\n\tAlignVerticalTop as MiddleLeftIcon,\r\n\tAlignVerticalCenter as MiddleCenterIcon,\r\n\tAlignVerticalBottom as MiddleRightIcon,\r\n\tAlignHorizontalLeft as BottomLeftIcon,\r\n\tAlignHorizontalCenter as BottomCenterIcon,\r\n\tAlignHorizontalRight as BottomRightIcon,\r\n\tPadding,\r\n} from \"@mui/icons-material\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore, { CANVAS_DEFAULT_VALUE, CANVAS_DEFAULT_VALUE_HOTSPOT, TCan<PERSON> } from \"../../../store/drawerStore\";\r\nimport { tooltipLeft, tooltipRight, tooltipTop, tooltipBottom, tooltipCenter, warning } from \"../../../assets/icons/icons\";\r\nimport { TouchAppSharp } from \"@mui/icons-material\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst TooltipCanvasSettings = ({ zindeex, setZindeex, setShowTooltipCanvasSettings }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetTooltipXaxis,\r\n\t\tsetTooltipYaxis,\r\n\t\tupdateCanvasInTooltip,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\ttooltipWidth,\r\n\t\tsetTooltipWidth,\r\n\t\tsetTooltipPadding,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\ttooltipBackgroundImage,\r\n\t\tsetTooltipBackgroundImage,\r\n\t\ttooltippadding,\r\n\t\ttooltipborderradius,\r\n\t\ttooltipbordersize,\r\n\t\ttooltipBordercolor,\r\n\t\ttooltipBackgroundcolor,\r\n\t\ttooltipPosition,\r\n\t\tsetTooltipPosition,\r\n\t\tsetElementSelected,\r\n\t\tsetIsTooltipPopup,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tcurrentStep,\r\n\t\tautoPosition,\r\n\t\tsetAutoPosition,\r\n\t\tselectedTemplate,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\tselectedTemplateTour,\r\n\t\tCANVAS_DEFAULT_VALUE_HOTSPOT,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [dismiss, setDismiss] = useState(false);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [error, setError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(tooltipBordercolor);\r\n\tconst [imagePreview, setImagePreview] = useState<string>(tooltipBackgroundImage);\r\n\r\n\tuseEffect(() => {\r\n\t\t// Sync tempBorderColor with store, using default if empty or transparent\r\n\t\tconst validColor = tooltipBordercolor && tooltipBordercolor !== \"transparent\" && tooltipBordercolor !== \"\" ? tooltipBordercolor : \"#000000\";\r\n\t\tsetTempBorderColor(validColor);\r\n\t  }, [tooltipBordercolor]);\r\n\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\", { defaultValue: \"Top Left\" }), icon: <TopLeftIcon fontSize=\"small\" />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\", { defaultValue: \"Top Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipTop }} style={{ fontSize: \"small\" }} /> : <TopCenterIcon fontSize=\"small\" />, value: \"top\" },\r\n\t\t{ label: translate(\"Top Right\", { defaultValue: \"Top Right\" }), icon: <TopRightIcon fontSize=\"small\" />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\", { defaultValue: \"Middle Left\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} style={{ fontSize: \"small\" }} /> : <MiddleLeftIcon fontSize=\"small\" />, value: \"left\" },\r\n\t\t{ label: translate(\"Middle Center\", { defaultValue: \"Middle Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} style={{ fontSize: \"small\" }} /> : <MiddleCenterIcon fontSize=\"small\" />, value: \"center\" },\r\n\t\t{ label: translate(\"Middle Right\", { defaultValue: \"Middle Right\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipRight }} style={{ fontSize: \"small\" }} /> : <MiddleRightIcon fontSize=\"small\" />, value: \"right\" },\r\n\t\t{ label: translate(\"Bottom Left\", { defaultValue: \"Bottom Left\" }), icon: <BottomLeftIcon fontSize=\"small\" />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\", { defaultValue: \"Bottom Center\" }), icon: selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\" ? <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} style={{ fontSize: \"small\" }} /> : <BottomCenterIcon fontSize=\"small\" />, value: \"bottom\" },\r\n\t\t{ label: translate(\"Bottom Right\", { defaultValue: \"Bottom Right\" }), icon: <BottomRightIcon fontSize=\"small\" />, value: \"bottom-right\" },\r\n\t];\r\n\r\n\tconst handlePositionClick = (e: any) => {\r\n\t\tif (e?.target?.id) {\r\n\t\t\t//setSelectedPosition(e.target.id);\r\n\t\t\tsetTooltipPosition(e.target.id);\r\n\t\t}\r\n\t};\r\n\r\n\tconst onReselectElement = () => {\r\n\r\n\t\tTooltipCanvasSettings({\r\n\t\t\tReSelection: false,\r\n\t\t\tXPosition: 4,\r\n\t\t\tYPosition: 4,\r\n\t\t\twidth: \"300\",\r\n\t\t\tPadding: \"2\",\r\n\t\t\tborderradius: \"8\",\r\n\t\t\tbordersize: \"0\",\r\n\t\t\tborderColor: \"\",\r\n\t\t\tbackgroundColor: \"\",\r\n\t\t\t// PulseAnimation: true,\r\n\t\t\t// stopAnimationUponInteraction: true,\r\n\t\t\t// ShowUpon: \"Hovering Hotspot\",\r\n\t\t\tShowByDefault: false,\r\n\t\t});\r\n\t\tupdateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTempBorderColor(e.target.value);\r\n\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleBackgroundColorChange = (e: any) => {\r\n\t\tif (e?.target?.value) {\r\n\t\t\tsetTooltipBackgroundcolor(e.target.value);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleBackgroundImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onload = (e) => {\r\n\t\t\t\tconst result = e.target?.result as string;\r\n\t\t\t\tsetTooltipBackgroundImage(result);\r\n\t\t\t\tsetImagePreview(result);\r\n\t\t\t\tsetIsUnSavedChanges(true);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClearBackgroundImage = () => {\r\n\t\tsetTooltipBackgroundImage(\"\");\r\n\t\tsetImagePreview(\"\");\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tconst handleAutoSelect = (e: any) => {\r\n\t\t//\tsetDismiss(e.target.checked);\r\n\t\tsetAutoPosition(e.target.checked);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Create the new canvas settings\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tposition: tooltipPosition,\r\n\t\t\tbackgroundColor: tooltipBackgroundcolor,\r\n\t\t\tbackgroundImage: tooltipBackgroundImage,\r\n\t\t\twidth: tooltipWidth,\r\n\t\t\tborderRadius: tooltipborderradius,\r\n\t\t\tpadding: tooltippadding,\r\n\t\t\tborderColor: tempBorderColor,\r\n\t\t\tborderSize: tooltipbordersize,\r\n\t\t\tautoposition: autoPosition,\r\n\t\t\txaxis: tooltipXaxis,\r\n\t\t\tyaxis: tooltipYaxis,\r\n\t\t};\r\n\r\n\t\t// Apply the changes - updateCanvasInTooltip will handle recording the change for undo/redo\r\n\t\tupdateCanvasInTooltip(updatedCanvasSettings); // Updates canvas and tooltipBordercolor\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (toolTipGuideMetaData[currentStep - 1]?.canvas) {\r\n\t\t\tconst canvasData = toolTipGuideMetaData[currentStep - 1].canvas;\r\n\r\n\t\t\t// Handle border color - use default if empty, transparent, or invalid\r\n\t\t\tconst borderColor = canvasData?.borderColor;\r\n\t\t\tconst validBorderColor = borderColor && borderColor !== \"transparent\" && borderColor !== \"\" ? borderColor : \"#000000\";\r\n\r\n\t\t\tsetTooltipPosition(canvasData?.position || \"middle-center\");\r\n\t\t\tsetTooltipPadding(canvasData?.padding || \"10px\");\r\n\t\t\tsetTooltipBorderradius(canvasData?.borderRadius || \"8px\");\r\n\t\t\tsetTooltipBordersize(canvasData?.borderSize || \"0px\");\r\n\t\t\tsetTooltipBordercolor(borderColor || \"\");\r\n\t\t\tsetTempBorderColor(validBorderColor); // Use valid color for the input\r\n\t\t\tsetTooltipBackgroundcolor(canvasData?.backgroundColor || \"#FFFFFF\");\r\n\t\t\tsetTooltipWidth(canvasData?.width || \"300px\");\r\n\t\t\tif (selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"2px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"2px\");\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tsetTooltipXaxis(canvasData?.xaxis || \"100px\");\r\n\t\t\t\tsetTooltipYaxis(canvasData?.yaxis || \"100px\");\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}, [toolTipGuideMetaData[currentStep - 1]?.canvas]);\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst formatValueWithPixelOrPercentage = (value: string) => {\r\n\t\tconst v = String(value);\r\n\t\tlet newValue = v;\r\n\t\tif (v?.endsWith(\"px\") || v?.endsWith(\"%\")) {\r\n\t\t\tnewValue = v.split(/px|%/)[0];\r\n\t\t}\r\n\t\treturn newValue;\r\n\t};\r\n\tconst handleChange = (e: any) => {\r\n\t\t// Only allow numeric input\r\n\t\tconst value = e.target.value;\r\n\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tlet inputValue = parseInt(value) || 0;\r\n\r\n\t\tsetTooltipWidth(`${inputValue}px`);\r\n\t};\r\n\r\n\r\n\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? translate(\"Canvas\") : translate(\"Canvas\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\t//startIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t\t\tendIcon={<TouchAppSharp />}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\tReselect Element\r\n\t\t\t\t\t\t\t\t</Button> */}\r\n\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Auto Position\")}\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={autoPosition}\r\n        onChange={handleAutoSelect}\r\n        name=\"autoPosition\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-position-grid\"\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\topacity: selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"? 0.5 : 1,\r\n\t\t\t\t\t\t\t\tcursor:selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"?\"not-allowed\":\"\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\"\r\n\t\t\t\t\t\t\t>{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t\t\t<Grid container spacing={1}>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[0].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[1].value}\r\n      onClick={() => {\r\n\r\n        //setSelectedPosition(positions[1].value);\r\n        setTooltipPosition(positions[1].value);\r\n      }}\r\n\tdisabled={autoPosition || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\"}\r\n\tdisableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[1].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipTop }}  id={positions[1].value} />\r\n      ) : (\r\n        <TopCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[2].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Top Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[3].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[3].value);\r\n        setTooltipPosition(positions[3].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[3].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipLeft }} id={positions[3].value} />\r\n      ) : (\r\n        <MiddleLeftIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[4].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[4].value);\r\n        setTooltipPosition(positions[4].value);\r\n      }}\r\n\t\t\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t\t\tdisableRipple\r\n      sx={{\r\n\t\t  opacity: tooltipPosition === positions[4].value ? 1 : 0.5,\r\n\t\t  paddingLeft:\"0 !important\"\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipCenter }} id={positions[4].value}/>\r\n      ) : (\r\n        <MiddleCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[5].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[5].value);\r\n        setTooltipPosition(positions[5].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[5].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipRight }}  id={positions[5].value} />\r\n      ) : (\r\n        <MiddleRightIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[6].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Left - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      id={positions[7].value}\r\n      onClick={() => {\r\n       // setSelectedPosition(positions[7].value);\r\n        setTooltipPosition(positions[7].value);\r\n      }}\r\n      disabled={autoPosition}\r\n\t  disableRipple\r\n      sx={{\r\n        opacity: tooltipPosition === positions[7].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") ? (\r\n        <span dangerouslySetInnerHTML={{ __html: tooltipBottom }} id={positions[7].value} />\r\n      ) : (\r\n        <BottomCenterIcon fontSize=\"small\" />\r\n      )}\r\n    </IconButton>\r\n  </Grid>\r\n  <Grid item xs={4}>\r\n    <IconButton\r\n      size=\"small\"\r\n      disabled={autoPosition}\r\n      sx={{\r\n        opacity: selectedPosition === positions[8].value ? 1 : 0.5,\r\n      }}\r\n    >\r\n      {/* Bottom Right - Keep empty or use default MUI icon */}\r\n    </IconButton>\r\n  </Grid>\r\n</Grid>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{/* Width Control */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">X {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipXaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipXaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">Y {translate(\"Axis Offset\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipYaxis)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => setTooltipYaxis(`${parseInt(e.target.value) || 0}px`)}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={autoPosition}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tstyle={{ flexDirection: \"column\", height: \"auto\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Width\")}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipWidth)}\r\n\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleChange}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Padding\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltippadding)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipPadding(`${inputValue}px`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Corner Radius\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tvalue={formatValueWithPixelOrPercentage(tooltipborderradius)}\r\n\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\t\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetTooltipBorderradius(`${inputValue}`);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t)}\r\n\r\n\r\n<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border Size\")}</div>\r\n  <div>\r\n    <TextField\r\n      variant=\"outlined\"\r\n      value={formatValueWithPixelOrPercentage(tooltipbordersize)}\r\n      size=\"small\"\r\n      className=\"qadpt-control-input\"\r\n      onChange={(e) => {\r\n        // Only allow numeric input\r\n        const value = e.target.value;\r\n        if (!/^-?\\d*$/.test(value)) {\r\n          return;\r\n        }\r\n        const inputValue = parseInt(value) || 0;\r\n        // Validate border size between 0px and 20px\r\n        if (inputValue < 0 || inputValue > 5) {\r\n          setBorderSizeError(true);\r\n        } else {\r\n          setBorderSizeError(false);\r\n        }\r\n        setTooltipBordersize(`${inputValue}px`);\r\n      }}\r\n      InputProps={{\r\n        endAdornment: \"px\",\r\n        sx: {\r\n          \"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n          \"& fieldset\": { border: \"none\" },\r\n        },\r\n      }}\r\n      error={borderSizeError}\r\n    />\r\n  </div>\r\n</Box>\r\n{borderSizeError && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    Value must be between 0px and 5px.\r\n  </Typography>\r\n)}\r\n\r\n{/* Button Selection Dropdown - Only show for Tooltip template */}\r\n{/* {(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n  <Box className=\"qadpt-control-box\" style={{ flexDirection: \"column\", height: \"auto\" }}>\r\n    <Typography className=\"qadpt-ctrl-title\">Selected Button</Typography>\r\n    <Box style={{ width: \"100%\" }}>\r\n      {toolTipGuideMetaData?.[currentStep - 1]?.containers?.[1]?.buttons ? (\r\n        <Select\r\n          value={\r\n            toolTipGuideMetaData[currentStep - 1].containers[1].buttons.find(\r\n              (button: any) =>\r\n                button.name === toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonName\r\n            )?.id || \"\"\r\n          }\r\n          onChange={(event) => {\r\n            const selectedValue = event.target.value;\r\n            const selectedButton = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.buttons?.find(\r\n              (button: any) => button.id === selectedValue\r\n            );\r\n\r\n            // Set dropdown value and button name\r\n            setDropdownValue(selectedValue);\r\n            setElementButtonName(selectedValue);\r\n            setbtnidss(selectedValue);\r\n            setElementClick(\"button\");\r\n\r\n            // Save button ID, button name, and \"NextStep\" in the Design object's \"goToNext\" object\r\n            const updatedCanvasSettings = {\r\n              NextStep: \"button\",\r\n              ButtonId: selectedValue,\r\n              ElementPath: \"\",\r\n              ButtonName: selectedButton?.name,\r\n            };\r\n\r\n            updateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n            // Set the button action to \"Next\" using updateTooltipButtonAction\r\n            if (selectedButton && selectedButton.id) {\r\n              // Find the container ID for the button\r\n              const containerId = toolTipGuideMetaData[currentStep - 1]?.containers[1]?.id;\r\n\r\n              // Update the button action to \"Next\"\r\n              updateTooltipButtonAction(containerId, selectedValue, {\r\n                value: \"Next\",\r\n                targetURL: \"\",\r\n                tab: \"same-tab\",\r\n                interaction: null,\r\n              });\r\n\r\n              // Set the selected action to \"Next\" in the UI\r\n              setSelectActions(\"Next\");\r\n\r\n              // Mark changes as unsaved\r\n              setIsUnSavedChanges(true);\r\n            }\r\n          }}\r\n          displayEmpty\r\n          style={{ width: \"100%\" }}\r\n          sx={{\r\n            \"& .MuiSelect-select\": {\r\n              textAlign: \"left\",\r\n              padding: \"9px 14px !important\",\r\n            },\r\n            \"& .MuiSvgIcon-root\": {\r\n              height: \"20px\",\r\n              width: \"20px\",\r\n              top: \"10px\",\r\n            },\r\n          }}\r\n        >\r\n          <MenuItem value=\"\" disabled>\r\n            Select a button\r\n          </MenuItem>\r\n          {toolTipGuideMetaData[currentStep - 1].containers[1].buttons.map(\r\n            (button: any, buttonIndex: number) => (\r\n              <MenuItem key={buttonIndex} value={button.id}>\r\n                {button.name}\r\n              </MenuItem>\r\n            )\r\n          )}\r\n        </Select>\r\n      ) : (\r\n        <Typography variant=\"body2\" color=\"textSecondary\">\r\n          No buttons available\r\n        </Typography>\r\n      )}\r\n    </Box>\r\n  </Box>\r\n)} */}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Border\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tempBorderColor || \"#000000\"}\r\n\t\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\tvalue={tooltipBackgroundcolor}\r\n\t\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t{/* Background Image Control */}\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Background Image\")}</div>\r\n\t\t\t\t\t\t\t<div style={{ display: \"flex\", flexDirection: \"column\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"file\"\r\n\t\t\t\t\t\t\t\t\taccept=\"image/*\"\r\n\t\t\t\t\t\t\t\t\tonChange={handleBackgroundImageChange}\r\n\t\t\t\t\t\t\t\t\tstyle={{ display: \"none\" }}\r\n\t\t\t\t\t\t\t\t\tid=\"tooltip-background-image-upload\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<label htmlFor=\"tooltip-background-image-upload\">\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tcomponent=\"span\"\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ textTransform: \"none\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Upload Image\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t{(tooltipBackgroundImage || imagePreview) && (\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={imagePreview || tooltipBackgroundImage}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"Background preview\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: \"cover\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ddd\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleClearBackgroundImage}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ textTransform: \"none\", minWidth: \"auto\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Clear\")}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t<Button\r\n\t\t\t\tvariant=\"contained\"\r\n\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\tclassName={`qadpt-btn ${paddingError || cornerRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\tdisabled={paddingError || cornerRadiusError || borderSizeError} // Disable button if any validation errors exist\r\n\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t</Button>\r\n\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default TooltipCanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAyB,eAAe;AACrG,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SACCC,mBAAmB,IAAIC,WAAW,EAClCC,qBAAqB,IAAIC,aAAa,EACtCC,oBAAoB,IAAIC,YAAY,EACpCC,gBAAgB,IAAIC,cAAc,EAClCC,mBAAmB,IAAIC,gBAAgB,EACvCC,mBAAmB,IAAIC,eAAe,EACtCX,mBAAmB,IAAIY,cAAc,EACrCV,qBAAqB,IAAIW,gBAAgB,EACzCT,oBAAoB,IAAIU,eAAe,QAEjC,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,IAAIC,oBAAoB,QAA+C,4BAA4B;AACxH,SAASC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAE1H,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EAC7F,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,eAAe;IACfC,eAAe;IACfC,qBAAqB;IACrBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,sBAAsB;IACtBC,oBAAoB;IACpBC,qBAAqB;IACrBC,yBAAyB;IACzBC,sBAAsB;IACtBC,yBAAyB;IACzBC,cAAc;IACdC,mBAAmB;IACnBC,iBAAiB;IACjBC,kBAAkB;IAClBC,sBAAsB;IACtBC,eAAe;IACfC,kBAAkB;IAClBC,kBAAkB;IAClBC,iBAAiB;IACjBC,oBAAoB;IACpBC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,gBAAgB;IAChBC,4BAA4B;IAC5BC,oBAAoB;IACpBC,4BAA4B;IAC5BC;EACD,CAAC,GAAGlD,cAAc,CAAEmD,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC8E,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAACkF,KAAK,EAAEC,QAAQ,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC4D,kBAAkB,CAAC;EAC1E,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAASuD,sBAAsB,CAAC;EAEhFxD,SAAS,CAAC,MAAM;IACf;IACA,MAAM+F,UAAU,GAAGlC,kBAAkB,IAAIA,kBAAkB,KAAK,aAAa,IAAIA,kBAAkB,KAAK,EAAE,GAAGA,kBAAkB,GAAG,SAAS;IAC3I+B,kBAAkB,CAACG,UAAU,CAAC;EAC7B,CAAC,EAAE,CAAClC,kBAAkB,CAAC,CAAC;EAE1B,MAAMmC,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEtD,SAAS,CAAC,UAAU,EAAE;MAAEuD,YAAY,EAAE;IAAW,CAAC,CAAC;IAAEC,IAAI,eAAEhE,OAAA,CAACxB,WAAW;MAACyF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EACzH;IAAER,KAAK,EAAEtD,SAAS,CAAC,YAAY,EAAE;MAAEuD,YAAY,EAAE;IAAa,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGtC,OAAA;MAAMuE,uBAAuB,EAAE;QAAEC,MAAM,EAAE9E;MAAW,CAAE;MAAC+E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACtB,aAAa;MAACuF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC3R;IAAER,KAAK,EAAEtD,SAAS,CAAC,WAAW,EAAE;MAAEuD,YAAY,EAAE;IAAY,CAAC,CAAC;IAAEC,IAAI,eAAEhE,OAAA,CAACpB,YAAY;MAACqF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC7H;IAAER,KAAK,EAAEtD,SAAS,CAAC,aAAa,EAAE;MAAEuD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGtC,OAAA;MAAMuE,uBAAuB,EAAE;QAAEC,MAAM,EAAEhF;MAAY,CAAE;MAACiF,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAAClB,cAAc;MAACmF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChS;IAAER,KAAK,EAAEtD,SAAS,CAAC,eAAe,EAAE;MAAEuD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGtC,OAAA;MAAMuE,uBAAuB,EAAE;QAAEC,MAAM,EAAE5E;MAAc,CAAE;MAAC6E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAAChB,gBAAgB;MAACiF,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEtD,SAAS,CAAC,cAAc,EAAE;MAAEuD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGtC,OAAA;MAAMuE,uBAAuB,EAAE;QAAEC,MAAM,EAAE/E;MAAa,CAAE;MAACgF,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACd,eAAe;MAAC+E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACrS;IAAER,KAAK,EAAEtD,SAAS,CAAC,aAAa,EAAE;MAAEuD,YAAY,EAAE;IAAc,CAAC,CAAC;IAAEC,IAAI,eAAEhE,OAAA,CAACb,cAAc;MAAC8E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACrI;IAAER,KAAK,EAAEtD,SAAS,CAAC,eAAe,EAAE;MAAEuD,YAAY,EAAE;IAAgB,CAAC,CAAC;IAAEC,IAAI,EAAE5B,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBAAGtC,OAAA;MAAMuE,uBAAuB,EAAE;QAAEC,MAAM,EAAE7E;MAAc,CAAE;MAAC8E,KAAK,EAAE;QAAER,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACZ,gBAAgB;MAAC6E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1S;IAAER,KAAK,EAAEtD,SAAS,CAAC,cAAc,EAAE;MAAEuD,YAAY,EAAE;IAAe,CAAC,CAAC;IAAEC,IAAI,eAAEhE,OAAA,CAACX,eAAe;MAAC4E,QAAQ,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CACzI;EAED,MAAMI,mBAAmB,GAAIC,CAAM,IAAK;IAAA,IAAAC,SAAA;IACvC,IAAID,CAAC,aAADA,CAAC,gBAAAC,SAAA,GAADD,CAAC,CAAEE,MAAM,cAAAD,SAAA,eAATA,SAAA,CAAWE,EAAE,EAAE;MAClB;MACAjD,kBAAkB,CAAC8C,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC;IAChC;EACD,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAE/B9E,qBAAqB,CAAC;MACrB+E,WAAW,EAAE,KAAK;MAClBC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,GAAG;MACjBC,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE,EAAE;MACnB;MACA;MACA;MACAC,aAAa,EAAE;IAChB,CAAC,CAAC;IACF9E,qBAAqB,CAACpB,oBAAoB,CAAC;IAC3CuC,kBAAkB,CAAC,IAAI,CAAC;IACxBC,iBAAiB,CAAC,KAAK,CAAC;IACxB3B,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EACD,MAAMsF,uBAAuB,GAAIf,CAAM,IAAK;IAAA,IAAAgB,UAAA;IAC3C,IAAIhB,CAAC,aAADA,CAAC,gBAAAgB,UAAA,GAADhB,CAAC,CAAEE,MAAM,cAAAc,UAAA,eAATA,UAAA,CAAWrB,KAAK,EAAE;MACrBb,kBAAkB,CAACkB,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAEnC;EACD,CAAC;EAED,MAAMsB,2BAA2B,GAAIjB,CAAM,IAAK;IAAA,IAAAkB,UAAA;IAC/C,IAAIlB,CAAC,aAADA,CAAC,gBAAAkB,UAAA,GAADlB,CAAC,CAAEE,MAAM,cAAAgB,UAAA,eAATA,UAAA,CAAWvB,KAAK,EAAE;MACrBlD,yBAAyB,CAACuD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC;IAC1C;EACD,CAAC;EAED,MAAMwB,2BAA2B,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACnF,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAAClB,MAAM,CAACqB,KAAK,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACT,MAAME,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAI1B,CAAC,IAAK;QAAA,IAAA2B,UAAA;QACtB,MAAMC,MAAM,IAAAD,UAAA,GAAG3B,CAAC,CAACE,MAAM,cAAAyB,UAAA,uBAARA,UAAA,CAAUC,MAAgB;QACzCjF,yBAAyB,CAACiF,MAAM,CAAC;QACjC5C,eAAe,CAAC4C,MAAM,CAAC;QACvB/D,mBAAmB,CAAC,IAAI,CAAC;MAC1B,CAAC;MACD2D,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMQ,0BAA0B,GAAGA,CAAA,KAAM;IACxCnF,yBAAyB,CAAC,EAAE,CAAC;IAC7BqC,eAAe,CAAC,EAAE,CAAC;IACnBnB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAMkE,gBAAgB,GAAI/B,CAAM,IAAK;IACpC;IACAxC,eAAe,CAACwC,CAAC,CAACE,MAAM,CAAC8B,OAAO,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBjE,SAAS,CAAC,KAAK,CAAC;IAChBvC,4BAA4B,CAAC,KAAK,CAAC;EACpC,CAAC;EAED,MAAMyG,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACA,MAAMC,qBAAqB,GAAG;MAC7BC,QAAQ,EAAEnF,eAAe;MACzB4D,eAAe,EAAE7D,sBAAsB;MACvCqF,eAAe,EAAE3F,sBAAsB;MACvC8D,KAAK,EAAErE,YAAY;MACnBmG,YAAY,EAAEzF,mBAAmB;MACjC0F,OAAO,EAAE3F,cAAc;MACvBgE,WAAW,EAAE/B,eAAe;MAC5B2D,UAAU,EAAE1F,iBAAiB;MAC7B2F,YAAY,EAAElF,YAAY;MAC1BmF,KAAK,EAAEzG,YAAY;MACnB0G,KAAK,EAAEzG;IACR,CAAC;;IAED;IACAF,qBAAqB,CAACmG,qBAAqB,CAAC,CAAC,CAAC;IAC9CF,WAAW,CAAC,CAAC;IACbpE,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED3E,SAAS,CAAC,MAAM;IAAA,IAAA0J,qBAAA;IACf,KAAAA,qBAAA,GAAIvF,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAAsF,qBAAA,eAArCA,qBAAA,CAAuCC,MAAM,EAAE;MAClD,MAAMC,UAAU,GAAGzF,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,CAACuF,MAAM;;MAE/D;MACA,MAAMjC,WAAW,GAAGkC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAElC,WAAW;MAC3C,MAAMmC,gBAAgB,GAAGnC,WAAW,IAAIA,WAAW,KAAK,aAAa,IAAIA,WAAW,KAAK,EAAE,GAAGA,WAAW,GAAG,SAAS;MAErH1D,kBAAkB,CAAC,CAAA4F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEV,QAAQ,KAAI,eAAe,CAAC;MAC3D/F,iBAAiB,CAAC,CAAAyG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEP,OAAO,KAAI,MAAM,CAAC;MAChDjG,sBAAsB,CAAC,CAAAwG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAER,YAAY,KAAI,KAAK,CAAC;MACzD/F,oBAAoB,CAAC,CAAAuG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEN,UAAU,KAAI,KAAK,CAAC;MACrDhG,qBAAqB,CAACoE,WAAW,IAAI,EAAE,CAAC;MACxC9B,kBAAkB,CAACiE,gBAAgB,CAAC,CAAC,CAAC;MACtCtG,yBAAyB,CAAC,CAAAqG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjC,eAAe,KAAI,SAAS,CAAC;MACnEzE,eAAe,CAAC,CAAA0G,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtC,KAAK,KAAI,OAAO,CAAC;MAC7C,IAAI/C,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,EAAE;QACzE7B,eAAe,CAAC,CAAAgH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,KAAK,CAAC;QAC3C3G,eAAe,CAAC,CAAA+G,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,KAAK,CAAC;MAC5C,CAAC,MACI;QACJ7G,eAAe,CAAC,CAAAgH,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,KAAK,KAAI,OAAO,CAAC;QAC7C3G,eAAe,CAAC,CAAA+G,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEH,KAAK,KAAI,OAAO,CAAC;MAE9C;IACD;EACD,CAAC,EAAE,EAAAhH,sBAAA,GAAC0B,oBAAoB,CAACC,WAAW,GAAG,CAAC,CAAC,cAAA3B,sBAAA,uBAArCA,sBAAA,CAAuCkH,MAAM,CAAC,CAAC;EAEnD,IAAI,CAAC9E,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMiF,gCAAgC,GAAIrD,KAAa,IAAK;IAC3D,MAAMsD,CAAC,GAAGC,MAAM,CAACvD,KAAK,CAAC;IACvB,IAAIwD,QAAQ,GAAGF,CAAC;IAChB,IAAIA,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,IAAI,CAAC,IAAIH,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEG,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1CD,QAAQ,GAAGF,CAAC,CAACI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9B;IACA,OAAOF,QAAQ;EAChB,CAAC;EACD,MAAMG,YAAY,GAAItD,CAAM,IAAK;IAChC;IACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;IAC5B,IAAI,CAAC,SAAS,CAAC4D,IAAI,CAAC5D,KAAK,CAAC,EAAE;MAC3B;IACD;IACA,IAAI6D,UAAU,GAAGC,QAAQ,CAAC9D,KAAK,CAAC,IAAI,CAAC;IAErCvD,eAAe,CAAC,GAAGoH,UAAU,IAAI,CAAC;EACnC,CAAC;EAID,oBACCnI,OAAA;IACC8E,EAAE,EAAC,mBAAmB;IACtBuD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7BtI,OAAA;MAAKqI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BtI,OAAA;QAAKqI,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCtI,OAAA,CAAC7B,UAAU;UACV,cAAW,MAAM;UACjBoK,OAAO,EAAE3B,WAAY;UAAA0B,QAAA,eAErBtI,OAAA,CAAC1B,2BAA2B;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbrE,OAAA;UAAKqI,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAGlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAI9B,SAAS,CAAC,QAAQ,CAAC,GAAGA,SAAS,CAAC,QAAQ;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvJrE,OAAA,CAAC7B,UAAU;UACVqK,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBD,OAAO,EAAE3B,WAAY;UAAA0B,QAAA,eAErBtI,OAAA,CAAC3B,SAAS;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNrE,OAAA;QAAKqI,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9BtI,OAAA;UAAKqI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9BtI,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAUjCtI,OAAA;cACCqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAG9B9H,SAAS,CAAC,eAAe;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACNrE,OAAA;cAAAsI,QAAA,eACAtI,OAAA;gBAAOqI,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACnCtI,OAAA;kBACIyI,IAAI,EAAC,UAAU;kBACf9B,OAAO,EAAEzE,YAAa;kBACtBwG,QAAQ,EAAEhC,gBAAiB;kBAC3BiC,IAAI,EAAC;gBAAc;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACFrE,OAAA;kBAAMqI,SAAS,EAAC;gBAAQ;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,qBAAqB;YACnCO,EAAE,EAAE;cACHC,OAAO,EAAEzG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAE,GAAG,GAAG,CAAC;cACtFwG,MAAM,EAAC1G,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,GAAC,aAAa,GAAC;YAC3F,CAAE;YAAAgG,QAAA,gBAEFtI,OAAA,CAAChC,UAAU;cAACqK,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EACtC9H,SAAS,CAAC,UAAU;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrCrE,OAAA,CAAC9B,IAAI;cAAC6K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAV,QAAA,gBAChCtI,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEjH,YAAa;kBACvB0G,EAAE,EAAE;oBACFC,OAAO,EAAE/F,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZ1D,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBiE,OAAO,EAAEA,CAAA,KAAM;oBAEb;oBACA1G,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACP6E,QAAQ,EAAEjH,YAAY,IAAIE,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAU;kBAC/F8G,aAAa;kBACRR,EAAE,EAAE;oBACFC,OAAO,EAAEjH,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAgE,QAAA,EAEAlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEtC,OAAA;oBAAMuE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9E;oBAAW,CAAE;oBAAEoF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFrE,OAAA,CAACtB,aAAa;oBAACuF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEjH,YAAa;kBACvB0G,EAAE,EAAE;oBACFC,OAAO,EAAE/F,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZ1D,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBiE,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC1G,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF6E,QAAQ,EAAEjH,YAAa;kBAC1BkH,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEjH,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAgE,QAAA,EAEAlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEtC,OAAA;oBAAMuE,uBAAuB,EAAE;sBAAEC,MAAM,EAAEhF;oBAAY,CAAE;oBAACsF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAElFrE,OAAA,CAAClB,cAAc;oBAACmF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACnC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZ1D,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBiE,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC1G,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACE6E,QAAQ,EAAEjH,YAAa;kBACvBkH,aAAa;kBACjBR,EAAE,EAAE;oBACNC,OAAO,EAAEjH,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG,GAAG;oBACzD+E,WAAW,EAAC;kBACV,CAAE;kBAAAf,QAAA,EAEAlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEtC,OAAA;oBAAMuE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE5E;oBAAc,CAAE;oBAACkF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,gBAEnFrE,OAAA,CAAChB,gBAAgB;oBAACiF,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZ1D,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBiE,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC1G,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF6E,QAAQ,EAAEjH,YAAa;kBAC1BkH,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEjH,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAgE,QAAA,EAEAlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEtC,OAAA;oBAAMuE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE/E;oBAAa,CAAE;oBAAEqF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFrE,OAAA,CAACd,eAAe;oBAAC+E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACpC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEjH,YAAa;kBACvB0G,EAAE,EAAE;oBACFC,OAAO,EAAE/F,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZ1D,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS,KAAM;kBACvBiE,OAAO,EAAEA,CAAA,KAAM;oBACd;oBACC1G,kBAAkB,CAACgC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;kBACxC,CAAE;kBACF6E,QAAQ,EAAEjH,YAAa;kBAC1BkH,aAAa;kBACVR,EAAE,EAAE;oBACFC,OAAO,EAAEjH,eAAe,KAAKiC,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACxD,CAAE;kBAAAgE,QAAA,EAEAlG,gBAAgB,KAAK,SAAS,IAAIE,oBAAoB,KAAK,SAAS,gBACpEtC,OAAA;oBAAMuE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7E;oBAAc,CAAE;oBAACmF,EAAE,EAAEjB,SAAS,CAAC,CAAC,CAAC,CAACS;kBAAM;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEpFrE,OAAA,CAACZ,gBAAgB;oBAAC6E,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACrC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACPrE,OAAA,CAAC9B,IAAI;gBAAC+K,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAZ,QAAA,eACftI,OAAA,CAAC7B,UAAU;kBACTqK,IAAI,EAAC,OAAO;kBACZW,QAAQ,EAAEjH,YAAa;kBACvB0G,EAAE,EAAE;oBACFC,OAAO,EAAE/F,gBAAgB,KAAKe,SAAS,CAAC,CAAC,CAAC,CAACS,KAAK,GAAG,CAAC,GAAG;kBACzD;gBAAE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAGNrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAC9H,SAAS,CAAC,aAAa,CAAC;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA,CAAC/B,SAAS;gBACTqL,OAAO,EAAC,UAAU;gBAClBhF,KAAK,EAAEqD,gCAAgC,CAAC/G,YAAY,CAAE;gBAEtD4H,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAG/D,CAAC,IAAKlE,eAAe,CAAC,GAAG2H,QAAQ,CAACzD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEiF,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAEjH;cAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,IAAE,EAAC9H,SAAS,CAAC,aAAa,CAAC;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA,CAAC/B,SAAS;gBACTqL,OAAO,EAAC,UAAU;gBAClBhF,KAAK,EAAEqD,gCAAgC,CAAC9G,YAAY,CAAE;gBAEtD2H,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAG/D,CAAC,IAAKjE,eAAe,CAAC,GAAG0H,QAAQ,CAACzD,CAAC,CAACE,MAAM,CAACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE;gBACvEiF,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFN,QAAQ,EAAEjH;cAAa;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA,CAACjC,GAAG;YACHsK,SAAS,EAAC,mBAAmB;YAC7B5D,KAAK,EAAE;cAAEiF,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE;YAAO,CAAE;YAAArB,QAAA,eAEnDtI,OAAA,CAACjC,GAAG;cAAC0G,KAAK,EAAE;gBAAEmF,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE,KAAK;gBAAE3E,KAAK,EAAE;cAAO,CAAE;cAAAmD,QAAA,gBAChFtI,OAAA;gBACCqI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAG9B9H,SAAS,CAAC,OAAO;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACNrE,OAAA;gBAAAsI,QAAA,eACAtI,OAAA,CAAC/B,SAAS;kBACTqL,OAAO,EAAC,UAAU;kBAClBhF,KAAK,EAAEqD,gCAAgC,CAAC7G,YAAY,CAAE;kBAEtD0H,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/BK,QAAQ,EAAET,YAAa;kBACvBsB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBZ,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEa,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF,CAAC,eAGNrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,SAAS;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA,CAAC/B,SAAS;gBACTqL,OAAO,EAAC,UAAU;gBAClBhF,KAAK,EAAEqD,gCAAgC,CAACpG,cAAc,CAAE;gBAExDiH,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAG/D,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC4D,IAAI,CAAC5D,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAM6D,UAAU,GAAGC,QAAQ,CAAC9D,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtChF,eAAe,CAAC,IAAI,CAAC;kBACtB,CAAC,MAAM;oBACNA,eAAe,CAAC,KAAK,CAAC;kBACvB;kBACAnC,iBAAiB,CAAC,GAAGmH,UAAU,IAAI,CAAC;gBACrC,CAAE;gBACFoB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFzG,KAAK,EAAEE;cAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLnB,YAAY,iBACblD,OAAA,CAAChC,UAAU;YACXyG,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChB8F,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAtI,OAAA;cAAMyE,KAAK,EAAE;gBAAEmF,OAAO,EAAE,MAAM;gBAAE3F,QAAQ,EAAE,MAAM;gBAAE4F,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9F7F,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3E;cAAQ;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE7D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAEDrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,eAAe;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA,CAAC/B,SAAS;gBACTqL,OAAO,EAAC,UAAU;gBAClBhF,KAAK,EAAEqD,gCAAgC,CAACnG,mBAAmB,CAAE;gBAE7DgH,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAG/D,CAAC,IAAK;kBAChB;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC4D,IAAI,CAAC5D,KAAK,CAAC,EAAE;oBAC3B;kBACD;kBACA,MAAM6D,UAAU,GAAGC,QAAQ,CAAC9D,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;oBACtC9E,oBAAoB,CAAC,IAAI,CAAC;kBAC3B,CAAC,MAAM;oBACNA,oBAAoB,CAAC,KAAK,CAAC;kBAC5B;kBACApC,sBAAsB,CAAC,GAAGkH,UAAU,EAAE,CAAC;gBACxC,CAAE;gBACFoB,UAAU,EAAE;kBACXC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM;kBAE5B;gBACD,CAAE;gBACFzG,KAAK,EAAEI;cAAkB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLjB,iBAAiB,iBAClBpD,OAAA,CAAChC,UAAU;YACXyG,KAAK,EAAE;cACNR,QAAQ,EAAE,MAAM;cAChB8F,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACV,CAAE;YAAAtB,QAAA,gBACAtI,OAAA;cAAMyE,KAAK,EAAE;gBAAEmF,OAAO,EAAE,MAAM;gBAAE3F,QAAQ,EAAE,MAAM;gBAAE4F,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAC;cAAM,CAAE;cAE9F7F,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3E;cAAQ;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,EACE7D,SAAS,CAAC,qCAAqC,CAAC;UAAA;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CACZ,eAGPrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAC3BtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,aAAa;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1ErE,OAAA;cAAAsI,QAAA,eACEtI,OAAA,CAAC/B,SAAS;gBACRqL,OAAO,EAAC,UAAU;gBAClBhF,KAAK,EAAEqD,gCAAgC,CAAClG,iBAAiB,CAAE;gBAC3D+G,IAAI,EAAC,OAAO;gBACZH,SAAS,EAAC,qBAAqB;gBAC/BK,QAAQ,EAAG/D,CAAC,IAAK;kBACf;kBACA,MAAML,KAAK,GAAGK,CAAC,CAACE,MAAM,CAACP,KAAK;kBAC5B,IAAI,CAAC,SAAS,CAAC4D,IAAI,CAAC5D,KAAK,CAAC,EAAE;oBAC1B;kBACF;kBACA,MAAM6D,UAAU,GAAGC,QAAQ,CAAC9D,KAAK,CAAC,IAAI,CAAC;kBACvC;kBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;oBACpC5E,kBAAkB,CAAC,IAAI,CAAC;kBAC1B,CAAC,MAAM;oBACLA,kBAAkB,CAAC,KAAK,CAAC;kBAC3B;kBACArC,oBAAoB,CAAC,GAAGiH,UAAU,IAAI,CAAC;gBACzC,CAAE;gBACFoB,UAAU,EAAE;kBACVC,YAAY,EAAE,IAAI;kBAClBZ,EAAE,EAAE;oBACF,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAE;sBAAEA,MAAM,EAAE;oBAAO;kBACjC;gBACF,CAAE;gBACFzG,KAAK,EAAEM;cAAgB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLf,eAAe,iBACdtD,OAAA,CAAChC,UAAU;YACTyG,KAAK,EAAE;cACLR,QAAQ,EAAE,MAAM;cAChB8F,KAAK,EAAE,SAAS;cAChBC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPC,YAAY,EAAE,KAAK;cACnBP,OAAO,EAAE;YACX,CAAE;YAAAtB,QAAA,gBAEFtI,OAAA;cACEyE,KAAK,EAAE;gBAAEmF,OAAO,EAAE,MAAM;gBAAE3F,QAAQ,EAAE,MAAM;gBAAE4F,UAAU,EAAE,QAAQ;gBAAEO,WAAW,EAAE;cAAM,CAAE;cACvF7F,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3E;cAAQ;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,sCAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb,eA2FKrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,QAAQ;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA;gBACCyI,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAEd,eAAe,IAAI,SAAU;gBACpCkF,QAAQ,EAAEhD,uBAAwB;gBAClC2C,SAAS,EAAC;cAAmB;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,YAAY;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpErE,OAAA;cAAAsI,QAAA,eACAtI,OAAA;gBACCyI,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAE3C,sBAAuB;gBAC9B+G,QAAQ,EAAE9C,2BAA4B;gBACtCyC,SAAS,EAAC;cAAmB;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrE,OAAA,CAACjC,GAAG;YAACsK,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjCtI,OAAA;cAAKqI,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAE9H,SAAS,CAAC,kBAAkB;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1ErE,OAAA;cAAKyE,KAAK,EAAE;gBAAEmF,OAAO,EAAE,MAAM;gBAAEF,aAAa,EAAE,QAAQ;gBAAEI,GAAG,EAAE;cAAM,CAAE;cAAAxB,QAAA,gBACpEtI,OAAA;gBACCyI,IAAI,EAAC,MAAM;gBACX4B,MAAM,EAAC,SAAS;gBAChB3B,QAAQ,EAAE5C,2BAA4B;gBACtCrB,KAAK,EAAE;kBAAEmF,OAAO,EAAE;gBAAO,CAAE;gBAC3B9E,EAAE,EAAC;cAAiC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACFrE,OAAA;gBAAOsK,OAAO,EAAC,iCAAiC;gBAAAhC,QAAA,eAC/CtI,OAAA,CAAC5B,MAAM;kBACNkL,OAAO,EAAC,UAAU;kBAClBiB,SAAS,EAAC,MAAM;kBAChB/B,IAAI,EAAC,OAAO;kBACZ/D,KAAK,EAAE;oBAAE+F,aAAa,EAAE;kBAAO,CAAE;kBAAAlC,QAAA,EAEhC9H,SAAS,CAAC,cAAc;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACP,CAAChD,sBAAsB,IAAIqC,YAAY,kBACvC1D,OAAA;gBAAKyE,KAAK,EAAE;kBAAEmF,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAM,CAAE;gBAAAxB,QAAA,gBACjEtI,OAAA;kBACCyK,GAAG,EAAE/G,YAAY,IAAIrC,sBAAuB;kBAC5CqJ,GAAG,EAAC,oBAAoB;kBACxBjG,KAAK,EAAE;oBACNU,KAAK,EAAE,MAAM;oBACbwE,MAAM,EAAE,MAAM;oBACdgB,SAAS,EAAE,OAAO;oBAClB1D,YAAY,EAAE,KAAK;oBACnBwC,MAAM,EAAE;kBACT;gBAAE;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACFrE,OAAA,CAAC5B,MAAM;kBACNkL,OAAO,EAAC,UAAU;kBAClBd,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAE9B,0BAA2B;kBACpChC,KAAK,EAAE;oBAAE+F,aAAa,EAAE,MAAM;oBAAEI,QAAQ,EAAE;kBAAO,CAAE;kBAAAtC,QAAA,EAElD9H,SAAS,CAAC,OAAO;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNrE,OAAA;QAAKqI,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACnCtI,OAAA,CAAC5B,MAAM;UACPkL,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAE1B,kBAAmB;UAC5BwB,SAAS,EAAE,aAAanF,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;UACjG6F,QAAQ,EAAEjG,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB,CAAC;UAAA;UAAAgF,QAAA,EAE7D9H,SAAS,CAAC,OAAO;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAAChE,EAAA,CA31BIJ,qBAAqB;EAAA,QACDH,cAAc,EAkCnCR,cAAc;AAAA;AAAAuL,EAAA,GAnCb5K,qBAAqB;AA61B3B,eAAeA,qBAAqB;AAAC,IAAA4K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\selectedpopupfields\\\\PageInteraction.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport \"../../guideDesign/Canvas.module.css\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { warning } from \"../../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageInteractions = ({\n  setShowCanvasSettings,\n  resetHeightofBanner\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    selectedTemplate,\n    padding,\n    setPadding,\n    position,\n    setPosition,\n    radius,\n    setRadius,\n    borderSize,\n    setBorderSize,\n    setBorderColor,\n    borderColor,\n    setBackgroundColor,\n    backgroundColor,\n    backgroundC,\n    setBackgroundC,\n    bannerBackgroundImage,\n    setBannerBackgroundImage,\n    Bposition,\n    setBposition,\n    bpadding,\n    setbPadding,\n    Bbordercolor,\n    setBBorderColor,\n    BborderSize,\n    setBBorderSize,\n    zindeex,\n    setZindeex,\n    sectionColor,\n    setSectionColor,\n    setBannerCanvasSetting,\n    setOverlayEnabled,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const handlePositionChange = pos => {\n    setPosition(pos);\n  };\n  const handleBackgroundImageChange = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = e => {\n        var _e$target;\n        const result = (_e$target = e.target) === null || _e$target === void 0 ? void 0 : _e$target.result;\n        setTempBackgroundImage(result);\n        setImagePreview(result);\n        setIsUnSavedChanges(true);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClearBackgroundImage = () => {\n    setTempBackgroundImage(\"\");\n    setImagePreview(\"\");\n    setIsUnSavedChanges(true);\n  };\n  const [tempPadding, setTempPadding] = useState(bpadding);\n  const [tempBorderSize, setTempBorderSize] = useState(BborderSize);\n  const [tempZIndex, setTempZIndex] = useState(zindeex);\n  const [tempBorderColor, setTempBorderColor] = useState(Bbordercolor);\n  const [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);\n  const [tempSectionColor, setTempSectionColor] = useState(sectionColor);\n  const [tempPosition, setTempPosition] = useState(Bposition || \"Cover Top\");\n  const [tempBackgroundImage, setTempBackgroundImage] = useState(bannerBackgroundImage);\n  const [imagePreview, setImagePreview] = useState(bannerBackgroundImage);\n  const [isOpen, setIsOpen] = useState(true);\n  const [isColorChangesDone, setColorChangesDone] = useState(false);\n  const [isPaddingChangeDone, setPaddingChangeDone] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const handleApplyChanges = () => {\n    if (paddingError || borderSizeError) {\n      return;\n    }\n    // Update position\n    setBposition(tempPosition);\n\n    // Apply padding changes if they were made\n    const paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;\n\n    // Reset height of banner with the correct padding value\n    resetHeightofBanner(tempPosition, parseInt(paddingToUse || \"12\"), parseInt(BborderSize || \"2\"), true, parseInt(tempPadding || \"0\"));\n\n    // Update background image if changed\n    if (tempBackgroundImage !== bannerBackgroundImage) {\n      setBannerBackgroundImage(tempBackgroundImage);\n    }\n\n    // Create canvas settings object with all properties\n    const canvasSettings = {\n      Position: tempPosition,\n      BackgroundColor: isColorChangesDone ? backgroundC : undefined,\n      BackgroundImage: tempBackgroundImage,\n      Padding: paddingToUse || \"12\",\n      BorderColor: Bbordercolor,\n      BorderSize: BborderSize || \"2\",\n      Zindex: 9999,\n      sectionColor: sectionColor\n    };\n\n    // Remove undefined properties\n    Object.keys(canvasSettings).forEach(key => {\n      if (canvasSettings[key] === undefined) {\n        delete canvasSettings[key];\n      }\n    });\n\n    // Apply canvas settings\n    setBannerCanvasSetting(canvasSettings);\n\n    // Close the panel and mark changes as unsaved\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowCanvasSettings(false);\n  };\n  if (!isOpen) return null;\n  const buttonStyle = isSelected => ({\n    border: isSelected ? `1px solid var(--primarycolor)` : \"none\",\n    borderRadius: \"20px\",\n    backgroundColor: isSelected ? \"#d3d9d9\" : \"#f1ecec\",\n    color: \"#000\",\n    cursor: \"pointer\",\n    //isSelected ? \"pointer\" : \"not-allowed\",\n    boxShadow: isSelected ? \"0 0 2px rgba(0,0,0,0.2)\" : \"none\",\n    opacity: isSelected ? \"1\" : \"0.5\",\n    padding: \"5px\",\n    lineHeight: \"15px\"\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Canvas\", {\n            defaultValue: \"Canvas\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          alignItems: \"center\",\n          backgroundColor: \"var(--back-light-color)\",\n          borderRadius: \"var(--button-border-radius)\",\n          height: \"auto\",\n          margin: \"0 15px 5px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            fontWeight: \"600\"\n          },\n          children: translate(\"Position\", {\n            defaultValue: \"Position\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            padding: \"8px\",\n            placeContent: \"center\",\n            gap: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            style: buttonStyle(tempPosition !== \"Cover Top\"),\n            onClick: () => {\n              setTempPosition(\"Push Down\");\n              // When changing position, mark padding as changed to ensure it's applied\n              setPaddingChangeDone(true);\n            },\n            children: translate(\"Push Down\", {\n              defaultValue: \"Push Down\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: buttonStyle(tempPosition === \"Cover Top\"),\n            onClick: () => {\n              setTempPosition(\"Cover Top\");\n              // When changing position, mark padding as changed to ensure it's applied\n              setPaddingChangeDone(true);\n            },\n            children: translate(\"Cover Top\", {\n              defaultValue: \"Cover Top\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-controls\",\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Padding\", {\n              defaultValue: \"Padding\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            variant: \"outlined\",\n            value: bpadding,\n            fullWidth: true,\n            size: \"small\",\n            className: \"qadpt-control-input\",\n            onChange: e => {\n              let value = e.target.value;\n              if (value === '') {\n                value = '0';\n              }\n              if (!/^-?\\d*$/.test(value)) {\n                return;\n              }\n              const inputValue = parseInt(value) || 0;\n\n              // Validate padding between 0px and 20px\n              if (inputValue < 0 || inputValue > 20) {\n                setPaddingError(true);\n              } else {\n                setPaddingError(false);\n              }\n\n              // Update both the current padding and temp padding\n              setbPadding(value);\n              setTempPadding(value);\n              setPaddingChangeDone(true);\n            },\n            InputProps: {\n              endAdornment: \"px\",\n              sx: {\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              }\n            },\n            error: paddingError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 6\n        }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 10\n          }, this), translate(\"padding_range_error\", {\n            defaultValue: \"Value must be between 0px and 20px.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Border Size\", {\n              defaultValue: \"Border Size\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            variant: \"outlined\",\n            value: BborderSize,\n            fullWidth: true,\n            size: \"small\",\n            className: \"qadpt-control-input\",\n            onChange: e => {\n              let value = e.target.value;\n\n              // If the input is empty, set value to '0'\n              if (value === '') {\n                value = '0';\n              }\n\n              // Only allow numeric input\n              if (!/^-?\\d*$/.test(value)) {\n                return;\n              }\n              const inputValue = parseInt(value) || 0;\n\n              // Validate border size between 0px and 10px\n              if (inputValue < 0 || inputValue > 10) {\n                setBorderSizeError(true);\n              } else {\n                setBorderSizeError(false);\n              }\n              setBBorderSize(value);\n            },\n            InputProps: {\n              endAdornment: \"px\",\n              sx: {\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              }\n            },\n            error: borderSizeError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 6\n        }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 16\n          }, this), translate(\"border_size_range_error\", {\n            defaultValue: \"Value must be between 0px and 10px.\"\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 14\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Border\", {\n              defaultValue: \"Border\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"color\",\n            value: Bbordercolor,\n            onChange: e => setBBorderColor(e.target.value),\n            className: \"qadpt-color-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-control-box\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            className: \"qadpt-control-label\",\n            children: translate(\"Background\", {\n              defaultValue: \"Background\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"color\",\n            value: backgroundC,\n            onChange: e => {\n              setColorChangesDone(true);\n              setBackgroundC(e.target.value);\n            },\n            className: \"qadpt-color-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${paddingError || borderSizeError ? \"disabled\" : \"\"}`,\n          disabled: paddingError || borderSizeError,\n          children: translate(\"Apply\", {\n            defaultValue: \"Apply\"\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 3\n  }, this);\n};\n_s(PageInteractions, \"n4giSe1gGTWyaI5SvXFBl2iXo58=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = PageInteractions;\nexport default PageInteractions;\nvar _c;\n$RefreshReg$(_c, \"PageInteractions\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "ArrowBackIosNewOutlinedIcon", "CloseIcon", "useDrawerStore", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "PageInteractions", "setShowCanvasSettings", "resetHeightofBanner", "_s", "t", "translate", "selectedTemplate", "padding", "setPadding", "position", "setPosition", "radius", "setRadius", "borderSize", "setBorderSize", "setBorderColor", "borderColor", "setBackgroundColor", "backgroundColor", "backgroundC", "setBackgroundC", "bannerBackgroundImage", "setBannerBackgroundImage", "Bposition", "setBposition", "bpadding", "setbPadding", "Bbordercolor", "setBBorderColor", "BborderSize", "setBBorderSize", "zindeex", "setZindeex", "sectionColor", "setSectionColor", "setBannerCanvasSetting", "setOverlayEnabled", "setIsUnSavedChanges", "state", "handlePositionChange", "pos", "handleBackgroundImageChange", "event", "_event$target$files", "file", "target", "files", "reader", "FileReader", "onload", "e", "_e$target", "result", "setTempBackgroundImage", "setImagePreview", "readAsDataURL", "handleClearBackgroundImage", "tempPadding", "setTempPadding", "tempBorderSize", "setTempBorderSize", "tempZIndex", "setTempZIndex", "tempBorderColor", "setTempBorderColor", "tempBackgroundColor", "setTempBackgroundColor", "tempSectionColor", "setTempSectionColor", "tempPosition", "setTempPosition", "tempBackgroundImage", "imagePreview", "isOpen", "setIsOpen", "isColorChangesDone", "setColorChangesDone", "isPaddingChangeDone", "setPaddingChangeDone", "paddingError", "setPaddingError", "borderSizeError", "setBorderSizeError", "handleApplyChanges", "paddingToUse", "parseInt", "canvasSettings", "Position", "BackgroundColor", "undefined", "BackgroundImage", "Padding", "BorderColor", "BorderSize", "Zindex", "Object", "keys", "for<PERSON>ach", "key", "handleClose", "buttonStyle", "isSelected", "border", "borderRadius", "color", "cursor", "boxShadow", "opacity", "lineHeight", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "defaultValue", "size", "style", "alignItems", "height", "margin", "fontWeight", "display", "place<PERSON><PERSON>nt", "gap", "variant", "value", "fullWidth", "onChange", "test", "inputValue", "InputProps", "endAdornment", "sx", "error", "fontSize", "textAlign", "top", "left", "marginBottom", "marginRight", "dangerouslySetInnerHTML", "__html", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/PageInteraction.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport \"../../guideDesign/Canvas.module.css\";\r\nimport { Box, Typography, TextField, Grid, IconButton, <PERSON>lt<PERSON>, <PERSON><PERSON> } from \"@mui/material\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { warning } from \"../../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst PageInteractions = ({ setShowCanvasSettings,resetHeightofBanner }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tselectedTemplate,\r\n\t\tpadding,\r\n\t\tsetPadding,\r\n\t\tposition,\r\n\t\tsetPosition,\r\n\t\tradius,\r\n\t\tsetRadius,\r\n\t\tborderSize,\r\n\t\tsetBorderSize,\r\n\t\tsetBorderColor,\r\n\t\tborderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundColor,\r\n\t\tbackgroundC,\r\n\t\tsetBackgroundC,\r\n\t\tbannerBackgroundImage,\r\n\t\tsetBannerBackgroundImage,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tbpadding,\r\n\t\tsetbPadding,\r\n\t\tBbordercolor,\r\n\t\tsetBBorderColor,\r\n\t\tBborderSize,\r\n\t\tsetBBorderSize,\r\n\t\tzindeex,\r\n\t\tsetZindeex,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tsetBannerCanvasSetting,\r\n\t\tsetOverlayEnabled,\r\n\t\tsetIsUnSavedChanges\r\n\t} = useDrawerStore((state) => state);\r\n\r\n\tconst handlePositionChange = (pos: any) => {\r\n\t\tsetPosition(pos);\r\n\t};\r\n\r\n\tconst handleBackgroundImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\t\tif (file) {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onload = (e) => {\r\n\t\t\t\tconst result = e.target?.result as string;\r\n\t\t\t\tsetTempBackgroundImage(result);\r\n\t\t\t\tsetImagePreview(result);\r\n\t\t\t\tsetIsUnSavedChanges(true);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClearBackgroundImage = () => {\r\n\t\tsetTempBackgroundImage(\"\");\r\n\t\tsetImagePreview(\"\");\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\tconst [tempPadding, setTempPadding] = useState(bpadding);\r\n\tconst [tempBorderSize, setTempBorderSize] = useState(BborderSize);\r\n\tconst [tempZIndex, setTempZIndex] = useState(zindeex);\r\n\tconst [tempBorderColor, setTempBorderColor] = useState(Bbordercolor);\r\n\tconst [tempBackgroundColor, setTempBackgroundColor] = useState(backgroundC);\r\n\tconst [tempSectionColor, setTempSectionColor] = useState(sectionColor);\r\n\tconst [tempPosition, setTempPosition] = useState(Bposition || \"Cover Top\");\r\n\tconst [tempBackgroundImage, setTempBackgroundImage] = useState(bannerBackgroundImage);\r\n\tconst [imagePreview, setImagePreview] = useState<string>(bannerBackgroundImage);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [isColorChangesDone, setColorChangesDone] = useState(false);\r\n\tconst [isPaddingChangeDone, setPaddingChangeDone] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\r\n\tconst handleApplyChanges = () => {\r\n\t\tif (paddingError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t// Update position\r\n\t\tsetBposition(tempPosition);\r\n\r\n\t\t// Apply padding changes if they were made\r\n\t\tconst paddingToUse = isPaddingChangeDone ? bpadding : tempPadding;\r\n\r\n\t\t// Reset height of banner with the correct padding value\r\n\t\tresetHeightofBanner(\r\n\t\t\ttempPosition,\r\n\t\t\tparseInt(paddingToUse || \"12\"),\r\n\t\t\tparseInt(BborderSize || \"2\"),\r\n\t\t\ttrue,\r\n\t\t\tparseInt(tempPadding || \"0\")\r\n\t\t);\r\n\r\n\t\t// Update background image if changed\r\n\t\tif (tempBackgroundImage !== bannerBackgroundImage) {\r\n\t\t\tsetBannerBackgroundImage(tempBackgroundImage);\r\n\t\t}\r\n\r\n\t\t// Create canvas settings object with all properties\r\n\t\tconst canvasSettings: Record<string, any> = {\r\n\t\t\tPosition: tempPosition,\r\n\t\t\tBackgroundColor: isColorChangesDone ? backgroundC : undefined,\r\n\t\t\tBackgroundImage: tempBackgroundImage,\r\n\t\t\tPadding: paddingToUse || \"12\",\r\n\t\t\tBorderColor: Bbordercolor,\r\n\t\t\tBorderSize: BborderSize || \"2\",\r\n\t\t\tZindex: 9999,\r\n\t\t\tsectionColor: sectionColor,\r\n\t\t};\r\n\r\n\t\t// Remove undefined properties\r\n\t\tObject.keys(canvasSettings).forEach((key) => {\r\n\t\t\tif (canvasSettings[key] === undefined) {\r\n\t\t\t\tdelete canvasSettings[key];\r\n\t\t\t}\r\n\t\t});\r\n\r\n\t\t// Apply canvas settings\r\n\t\tsetBannerCanvasSetting(canvasSettings);\r\n\r\n\t\t// Close the panel and mark changes as unsaved\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst buttonStyle = (isSelected: boolean) => ({\r\n\t\tborder: isSelected ? `1px solid var(--primarycolor)` : \"none\",\r\n\t\tborderRadius: \"20px\",\r\n\t\tbackgroundColor: isSelected ? \"#d3d9d9\" : \"#f1ecec\",\r\n\t\tcolor: \"#000\",\r\n\t\tcursor: \"pointer\", //isSelected ? \"pointer\" : \"not-allowed\",\r\n\t\tboxShadow: isSelected ? \"0 0 2px rgba(0,0,0,0.2)\" : \"none\",\r\n\t\topacity: isSelected ? \"1\" : \"0.5\",\r\n\t\tpadding: \"5px\",\r\n\t\tlineHeight : \"15px\",\r\n\t});\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-designpopup\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\", { defaultValue: \"Canvas\" })}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tbackgroundColor: \"var(--back-light-color)\",\r\n\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\tmargin: \"0 15px 5px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<label style={{ fontWeight: \"600\" }}>{translate(\"Position\", { defaultValue: \"Position\" })}</label>\r\n\t\t\t\t\t<div style={{ display: \"flex\", padding: \"8px\", placeContent: \"center\", gap: \"5px\" }}>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition !== \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Push Down\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Push Down\", { defaultValue: \"Push Down\" })}\r\n\t\t\t\t\t\t</button>\r\n\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={buttonStyle(tempPosition === \"Cover Top\")}\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\tsetTempPosition(\"Cover Top\");\r\n\t\t\t\t\t\t\t\t// When changing position, mark padding as changed to ensure it's applied\r\n\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Cover Top\", { defaultValue: \"Cover Top\" })}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\", { defaultValue: \"Padding\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={bpadding}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n    \t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\r\n\t\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tvalue = '0';\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t// Update both the current padding and temp padding\r\n\t\t\t\t\t\t\t\t\tsetbPadding(value);\r\n\t\t\t\t\t\t\t\t\tsetTempPadding(value);\r\n\t\t\t\t\t\t\t\t\tsetPaddingChangeDone(true);\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"padding_range_error\", { defaultValue: \"Value must be between 0px and 20px.\" })}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\", { defaultValue: \"Border Size\" })}</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={BborderSize}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\r\n\t\t\t\t\t\t\t\t// If the input is empty, set value to '0'\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\tvalue = '0';\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetBBorderSize(value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t{translate(\"border_size_range_error\", { defaultValue: \"Value must be between 0px and 10px.\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={tempZIndex}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => setTempZIndex(parseInt(e.target.value) || 0)}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\", { defaultValue: \"Border\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={Bbordercolor}\r\n\t\t\t\t\t\t\tonChange={(e) => setBBorderColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\", { defaultValue: \"Background\" })}</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundC}\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetColorChangesDone(true);\r\n\t\t\t\t\t\t\t\tsetBackgroundC(e.target.value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Section Color</Typography>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={tempSectionColor}\r\n\t\t\t\t\t\t\tonChange={(e) => setTempSectionColor(e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${paddingError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={paddingError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\", { defaultValue: \"Apply\" })}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default PageInteractions;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,qCAAqC;AAC5C,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAWC,MAAM,QAAQ,eAAe;AAC7F,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,qBAAqB;EAACC;AAAyB,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGR,cAAc,CAAC,CAAC;EACzC,MAAM;IACLS,gBAAgB;IAChBC,OAAO;IACPC,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,MAAM;IACNC,SAAS;IACTC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,WAAW;IACXC,kBAAkB;IAClBC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC,qBAAqB;IACrBC,wBAAwB;IACxBC,SAAS;IACTC,YAAY;IACZC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,WAAW;IACXC,cAAc;IACdC,OAAO;IACPC,UAAU;IACVC,YAAY;IACZC,eAAe;IACfC,sBAAsB;IACtBC,iBAAiB;IACjBC;EACD,CAAC,GAAG1C,cAAc,CAAE2C,KAAK,IAAKA,KAAK,CAAC;EAEpC,MAAMC,oBAAoB,GAAIC,GAAQ,IAAK;IAC1C9B,WAAW,CAAC8B,GAAG,CAAC;EACjB,CAAC;EAED,MAAMC,2BAA2B,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACnF,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACC,KAAK,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAIC,IAAI,EAAE;MACT,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;QAAA,IAAAC,SAAA;QACtB,MAAMC,MAAM,IAAAD,SAAA,GAAGD,CAAC,CAACL,MAAM,cAAAM,SAAA,uBAARA,SAAA,CAAUC,MAAgB;QACzCC,sBAAsB,CAACD,MAAM,CAAC;QAC9BE,eAAe,CAACF,MAAM,CAAC;QACvBf,mBAAmB,CAAC,IAAI,CAAC;MAC1B,CAAC;MACDU,MAAM,CAACQ,aAAa,CAACX,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMY,0BAA0B,GAAGA,CAAA,KAAM;IACxCH,sBAAsB,CAAC,EAAE,CAAC;IAC1BC,eAAe,CAAC,EAAE,CAAC;IACnBjB,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EACD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAACsC,QAAQ,CAAC;EACxD,MAAM,CAACkC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC0C,WAAW,CAAC;EACjE,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC4C,OAAO,CAAC;EACrD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAACwC,YAAY,CAAC;EACpE,MAAM,CAACsC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/E,QAAQ,CAACgC,WAAW,CAAC;EAC3E,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC8C,YAAY,CAAC;EACtE,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAACoC,SAAS,IAAI,WAAW,CAAC;EAC1E,MAAM,CAACgD,mBAAmB,EAAElB,sBAAsB,CAAC,GAAGlE,QAAQ,CAACkC,qBAAqB,CAAC;EACrF,MAAM,CAACmD,YAAY,EAAElB,eAAe,CAAC,GAAGnE,QAAQ,CAASkC,qBAAqB,CAAC;EAC/E,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACwF,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0F,mBAAmB,EAAEC,oBAAoB,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMgG,kBAAkB,GAAGA,CAAA,KAAM;IAChC,IAAIJ,YAAY,IAAIE,eAAe,EAAE;MACpC;IACD;IACA;IACAzD,YAAY,CAAC6C,YAAY,CAAC;;IAE1B;IACA,MAAMe,YAAY,GAAGP,mBAAmB,GAAGpD,QAAQ,GAAGgC,WAAW;;IAEjE;IACAvD,mBAAmB,CAClBmE,YAAY,EACZgB,QAAQ,CAACD,YAAY,IAAI,IAAI,CAAC,EAC9BC,QAAQ,CAACxD,WAAW,IAAI,GAAG,CAAC,EAC5B,IAAI,EACJwD,QAAQ,CAAC5B,WAAW,IAAI,GAAG,CAC5B,CAAC;;IAED;IACA,IAAIc,mBAAmB,KAAKlD,qBAAqB,EAAE;MAClDC,wBAAwB,CAACiD,mBAAmB,CAAC;IAC9C;;IAEA;IACA,MAAMe,cAAmC,GAAG;MAC3CC,QAAQ,EAAElB,YAAY;MACtBmB,eAAe,EAAEb,kBAAkB,GAAGxD,WAAW,GAAGsE,SAAS;MAC7DC,eAAe,EAAEnB,mBAAmB;MACpCoB,OAAO,EAAEP,YAAY,IAAI,IAAI;MAC7BQ,WAAW,EAAEjE,YAAY;MACzBkE,UAAU,EAAEhE,WAAW,IAAI,GAAG;MAC9BiE,MAAM,EAAE,IAAI;MACZ7D,YAAY,EAAEA;IACf,CAAC;;IAED;IACA8D,MAAM,CAACC,IAAI,CAACV,cAAc,CAAC,CAACW,OAAO,CAAEC,GAAG,IAAK;MAC5C,IAAIZ,cAAc,CAACY,GAAG,CAAC,KAAKT,SAAS,EAAE;QACtC,OAAOH,cAAc,CAACY,GAAG,CAAC;MAC3B;IACD,CAAC,CAAC;;IAEF;IACA/D,sBAAsB,CAACmD,cAAc,CAAC;;IAEtC;IACAa,WAAW,CAAC,CAAC;IACb9D,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM8D,WAAW,GAAGA,CAAA,KAAM;IACzBzB,SAAS,CAAC,KAAK,CAAC;IAChBzE,qBAAqB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,IAAI,CAACwE,MAAM,EAAE,OAAO,IAAI;EAExB,MAAM2B,WAAW,GAAIC,UAAmB,KAAM;IAC7CC,MAAM,EAAED,UAAU,GAAG,+BAA+B,GAAG,MAAM;IAC7DE,YAAY,EAAE,MAAM;IACpBrF,eAAe,EAAEmF,UAAU,GAAG,SAAS,GAAG,SAAS;IACnDG,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,SAAS;IAAE;IACnBC,SAAS,EAAEL,UAAU,GAAG,yBAAyB,GAAG,MAAM;IAC1DM,OAAO,EAAEN,UAAU,GAAG,GAAG,GAAG,KAAK;IACjC9F,OAAO,EAAE,KAAK;IACdqG,UAAU,EAAG;EACd,CAAC,CAAC;EAEF,oBACC7G,OAAA;IAAK8G,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACjC/G,OAAA;MAAK8G,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B/G,OAAA;QAAK8G,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC/G,OAAA,CAACR,UAAU;UACV,cAAW,OAAO;UAClBwH,OAAO,EAAEZ,WAAY;UAAAW,QAAA,eAErB/G,OAAA,CAACN,2BAA2B;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbpH,OAAA;UAAK8G,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEzG,SAAS,CAAC,QAAQ,EAAE;YAAE+G,YAAY,EAAE;UAAS,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFpH,OAAA,CAACR,UAAU;UACV8H,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBN,OAAO,EAAEZ,WAAY;UAAAW,QAAA,eAErB/G,OAAA,CAACL,SAAS;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAENpH,OAAA;QACCuH,KAAK,EAAE;UACNC,UAAU,EAAE,QAAQ;UACpBrG,eAAe,EAAE,yBAAyB;UAC1CqF,YAAY,EAAE,6BAA6B;UAC3CiB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE;QACT,CAAE;QAAAX,QAAA,gBAEF/G,OAAA;UAAOuH,KAAK,EAAE;YAAEI,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAEzG,SAAS,CAAC,UAAU,EAAE;YAAE+G,YAAY,EAAE;UAAW,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClGpH,OAAA;UAAKuH,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEpH,OAAO,EAAE,KAAK;YAAEqH,YAAY,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACnF/G,OAAA;YACCuH,KAAK,EAAElB,WAAW,CAAC/B,YAAY,KAAK,WAAW,CAAE;YACjD0C,OAAO,EAAEA,CAAA,KAAM;cACdzC,eAAe,CAAC,WAAW,CAAC;cAC5B;cACAQ,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YAAAgC,QAAA,EAEDzG,SAAS,CAAC,WAAW,EAAE;cAAE+G,YAAY,EAAE;YAAY,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eAETpH,OAAA;YACCuH,KAAK,EAAElB,WAAW,CAAC/B,YAAY,KAAK,WAAW,CAAE;YACjD0C,OAAO,EAAEA,CAAA,KAAM;cACdzC,eAAe,CAAC,WAAW,CAAC;cAC5B;cACAQ,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YAAAgC,QAAA,EAEDzG,SAAS,CAAC,WAAW,EAAE;cAAE+G,YAAY,EAAE;YAAY,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENpH,OAAA;QAAK8G,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC9B/G,OAAA,CAACX,GAAG;UAACyH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC/G,OAAA,CAACV,UAAU;YAACwH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEzG,SAAS,CAAC,SAAS,EAAE;cAAE+G,YAAY,EAAE;YAAU,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC5GpH,OAAA,CAACT,SAAS;YACTwI,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAEtG,QAAS;YAChBuG,SAAS;YACTX,IAAI,EAAC,OAAO;YACZR,SAAS,EAAC,qBAAqB;YAC/BoB,QAAQ,EAAG/E,CAAC,IAAK;cACZ,IAAI6E,KAAK,GAAG7E,CAAC,CAACL,MAAM,CAACkF,KAAK;cAE7B,IAAIA,KAAK,KAAK,EAAE,EAAE;gBAClBA,KAAK,GAAG,GAAG;cACX;cAGA,IAAI,CAAC,SAAS,CAACG,IAAI,CAACH,KAAK,CAAC,EAAE;gBAC5B;cACA;cAEA,MAAMI,UAAU,GAAG9C,QAAQ,CAAC0C,KAAK,CAAC,IAAI,CAAC;;cAEvC;cACA,IAAII,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;gBACvCnD,eAAe,CAAC,IAAI,CAAC;cACrB,CAAC,MAAM;gBACPA,eAAe,CAAC,KAAK,CAAC;cACtB;;cAEA;cACAtD,WAAW,CAACqG,KAAK,CAAC;cAClBrE,cAAc,CAACqE,KAAK,CAAC;cACrBjD,oBAAoB,CAAC,IAAI,CAAC;YAC3B,CAAE;YACFsD,UAAU,EAAE;cACXC,YAAY,EAAE,IAAI;cAClBC,EAAE,EAAE;gBACJ,0CAA0C,EAAE;kBAAEhC,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAC/B;YACD,CAAE;YACFiC,KAAK,EAAExD;UAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EACLpC,YAAY,iBACZhF,OAAA,CAACV,UAAU;UACViI,KAAK,EAAE;YACNkB,QAAQ,EAAE,MAAM;YAChBhC,KAAK,EAAE,SAAS;YAChBiC,SAAS,EAAE,MAAM;YACjBC,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAE,KAAK;YACnBjB,OAAO,EAAE;UACV,CAAE;UAAAb,QAAA,gBACA/G,OAAA;YAAMuH,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEa,QAAQ,EAAE,MAAM;cAAEjB,UAAU,EAAE,QAAQ;cAAEsB,WAAW,EAAC;YAAM,CAAE;YAE9FC,uBAAuB,EAAE;cAAEC,MAAM,EAAEnJ;YAAQ;UAAE;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EACA9G,SAAS,CAAC,qBAAqB,EAAE;YAAE+G,YAAY,EAAE;UAAsC,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CACZ,eACDpH,OAAA,CAACX,GAAG;UAACyH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC/G,OAAA,CAACV,UAAU;YAACwH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEzG,SAAS,CAAC,aAAa,EAAE;cAAE+G,YAAY,EAAE;YAAc,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACpHpH,OAAA,CAACT,SAAS;YACTwI,OAAO,EAAC,UAAU;YAClBC,KAAK,EAAElG,WAAY;YACnBmG,SAAS;YACTX,IAAI,EAAC,OAAO;YACZR,SAAS,EAAC,qBAAqB;YAC/BoB,QAAQ,EAAG/E,CAAC,IAAK;cAChB,IAAI6E,KAAK,GAAG7E,CAAC,CAACL,MAAM,CAACkF,KAAK;;cAE1B;cACA,IAAIA,KAAK,KAAK,EAAE,EAAE;gBAClBA,KAAK,GAAG,GAAG;cACX;;cAEA;cACA,IAAI,CAAC,SAAS,CAACG,IAAI,CAACH,KAAK,CAAC,EAAE;gBAC5B;cACA;cAEA,MAAMI,UAAU,GAAG9C,QAAQ,CAAC0C,KAAK,CAAC,IAAI,CAAC;;cAEvC;cACA,IAAII,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;gBACvCjD,kBAAkB,CAAC,IAAI,CAAC;cACxB,CAAC,MAAM;gBACPA,kBAAkB,CAAC,KAAK,CAAC;cACzB;cAEApD,cAAc,CAACiG,KAAK,CAAC;YACtB,CAAE;YACFK,UAAU,EAAE;cACXC,YAAY,EAAE,IAAI;cAClBC,EAAE,EAAE;gBACJ,0CAA0C,EAAE;kBAAEhC,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAC/B;YACD,CAAE;YACFiC,KAAK,EAAEtD;UAAgB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EACLlC,eAAe,iBACflF,OAAA,CAACV,UAAU;UACXiI,KAAK,EAAE;YACNkB,QAAQ,EAAE,MAAM;YAChBhC,KAAK,EAAE,SAAS;YAChBiC,SAAS,EAAE,MAAM;YACjBC,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPC,YAAY,EAAE,KAAK;YACnBjB,OAAO,EAAE;UACV,CAAE;UAAAb,QAAA,gBACA/G,OAAA;YAAMuH,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEa,QAAQ,EAAE,MAAM;cAAEjB,UAAU,EAAE,QAAQ;cAAEsB,WAAW,EAAC;YAAM,CAAE;YAE9FC,uBAAuB,EAAE;cAAEC,MAAM,EAAEnJ;YAAQ;UAAE;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,EACN9G,SAAS,CAAC,yBAAyB,EAAE;YAAE+G,YAAY,EAAE;UAAsC,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CACZ,eAaRpH,OAAA,CAACX,GAAG;UAACyH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC/G,OAAA,CAACV,UAAU;YAACwH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEzG,SAAS,CAAC,QAAQ,EAAE;cAAE+G,YAAY,EAAE;YAAS,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1GpH,OAAA;YACCiJ,IAAI,EAAC,OAAO;YACZjB,KAAK,EAAEpG,YAAa;YACpBsG,QAAQ,EAAG/E,CAAC,IAAKtB,eAAe,CAACsB,CAAC,CAACL,MAAM,CAACkF,KAAK,CAAE;YACjDlB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNpH,OAAA,CAACX,GAAG;UAACyH,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBACjC/G,OAAA,CAACV,UAAU;YAACwH,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEzG,SAAS,CAAC,YAAY,EAAE;cAAE+G,YAAY,EAAE;YAAa,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClHpH,OAAA;YACCiJ,IAAI,EAAC,OAAO;YACZjB,KAAK,EAAE5G,WAAY;YACnB8G,QAAQ,EAAG/E,CAAC,IAAK;cAChB0B,mBAAmB,CAAC,IAAI,CAAC;cACzBxD,cAAc,CAAC8B,CAAC,CAACL,MAAM,CAACkF,KAAK,CAAC;YAC/B,CAAE;YACFlB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUF,CAAC,eACNpH,OAAA;QAAK8G,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC/G,OAAA,CAACP,MAAM;UACNsI,OAAO,EAAC,WAAW;UACnBf,OAAO,EAAE5B,kBAAmB;UAC5B0B,SAAS,EAAE,aAAa9B,YAAY,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;UAC5EgE,QAAQ,EAAElE,YAAY,IAAIE,eAAgB;UAAA6B,QAAA,EAE1CzG,SAAS,CAAC,OAAO,EAAE;YAAE+G,YAAY,EAAE;UAAQ,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAAChH,EAAA,CA7XIH,gBAAgB;EAAA,QACIH,cAAc,EAkCnCF,cAAc;AAAA;AAAAuJ,EAAA,GAnCblJ,gBAAgB;AA+XtB,eAAeA,gBAAgB;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}